FROM node:18.18-buster as build

WORKDIR /app
COPY . /app/

#RUN apt-get -yqq --no-install-recommends make g++ git

RUN npm install --unsafe-perm \
    && npm run clean \
    && npm run build

FROM node:18.18-buster

# These packages need for gc-stats compiling
#RUN apt-get -yqq --no-install-recommends  make g++

EXPOSE 6001

WORKDIR /app

COPY --chown=node:node --from=build /app/package.json /app/package-lock.json /app/.npmrc ./
COPY --chown=node:node --from=build /app/lib ./lib

RUN npm ci --production
RUN npm cache clean --force
RUN rm .npmrc

USER node
CMD ["node", "/app/lib/mainWallet"]
