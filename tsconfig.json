{"compilerOptions": {"target": "es2022", "lib": ["es2022", "ES2022.E<PERSON>r"], "module": "NodeNext", "esModuleInterop": false, "outDir": "lib", "allowSyntheticDefaultImports": true, "sourceMap": true, "isolatedModules": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": false, "noImplicitAny": false, "removeComments": true, "noLib": false, "preserveConstEnums": true, "suppressImplicitAnyIndexErrors": true, "inlineSources": false, "skipLibCheck": true, "ignoreDeprecations": "5.0", "useDefineForClassFields": false, "rootDir": "./src"}, "include": ["src/**/*.ts", "resources"], "exclude": ["node_modules"]}