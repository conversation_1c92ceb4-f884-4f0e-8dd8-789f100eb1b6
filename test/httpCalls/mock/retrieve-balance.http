GET http://localhost:6003/v1/merchant/swftest/customer/Customer123/ticket

> {% client.global.set("customer_ticket", response.body.trim()); %}

###

POST http://localhost:6003/BetfairGamingInterfaceService/v1.0 HTTP/1.1
Content-Type: application/xml
x-sw-player-token: {{customer_ticket}}

<?xml version="1.0" encoding="utf-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                  xmlns:bet="http://www.betfair.com/servicetypes/v1/BetfairGamingInterface/">
    <soapenv:Body>
        <bet:RetrieveBalanceRequest xmlns:bet="http://www.betfair.com/serviceapi/v1.0/BetfairGamingInterface/">
            <bet:balanceRequest>
                <bet:baseRequest>
                    <bet:rgsAuthentication>
                        <bet:rgsUser>SkyWind</bet:rgsUser>
                        <bet:rgsPassword>***</bet:rgsPassword>
                    </bet:rgsAuthentication>
                    <bet:userAuthentication>
                        <bet:accountId>*********</bet:accountId>
                        <bet:token>TICKET-Customer123-*************-508428</bet:token>
                        <bet:authenticationType>ONLINE</bet:authenticationType>
                    </bet:userAuthentication>
                    <bet:requestId>29617b63-b595-4fee-bb12-d07c7030598c</bet:requestId>
                    <bet:timestamp>*************</bet:timestamp>
                </bet:baseRequest>
                <bet:productName>gaming</bet:productName>
                <bet:gameCode>sw_live_erol_atom</bet:gameCode>
            </bet:balanceRequest>
        </bet:RetrieveBalanceRequest>
    </soapenv:Body>
</soapenv:Envelope>

###
