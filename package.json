{"name": "sw-integration-betfair", "version": "1.7.0", "private": true, "typings": "resources/index.d.ts", "scripts": {"clean": "rm -rf ./lib", "build": "npm run version && nest build", "format": "prettier --write \"**/*.ts\"", "build:dev": "nest build --watch", "start:launcher": "nest start launcher", "start:launcher:dev": "env-cmd nest start launcher --watch", "start:launcher:debug": "env-cmd nest start launcher --debug --watch", "start:wallet": "nest start wallet", "start:wallet:dev": "env-cmd nest start wallet --watch", "start:wallet:debug": "env-cmd nest start wallet --debug --watch", "start:mock": "env-cmd nest start mock", "start:mock:dev": "env-cmd nest start mock --watch", "start:mock:pref": "env-cmd NOT_SAVE_ANY_DATA=true MOCK_CONTENT_TYPE='text/xml' nest start mock --watch", "start:mock:debug": "env-cmd nest start mock --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs}/**/*.ts\" --fix", "test": "jest --coverage", "test:coverage": "npm run lint && nyc --reporter=lcov --reporter=lcovonly --reporter=text npm run test", "version": "mkdir -p lib && echo $npm_package_version $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/version"}, "dependencies": {"@nestjs/common": "7.4.2", "@nestjs/core": "7.4.2", "@nestjs/platform-fastify": "7.0.9", "@skywind-group/sw-currency-exchange": "1.4.23", "@skywind-group/sw-integration-core": "1.0.83", "@skywind-group/sw-utils": "^1.0.27", "@skywind-group/sw-wallet-adapter-core": "^0.6.174", "bole": "3.0.2", "bole-console": "0.1.10", "class-transformer": "^0.4.0", "class-validator": "^0.12.2", "fastify": "2.14.0", "nestjs-soap": "2.3.0", "soap": "1.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^7.0.0", "@nestjs/testing": "7.4.2", "@types/chai": "^4.3.6", "@types/jest": "^29.5.11", "@types/mocha": "^10.0.2", "@types/node": "^20.7.0", "@types/sinon": "^9.0.4", "@types/soap": "0.21.0", "@types/supertest": "^2.0.8", "@types/validator": "10.11.3", "@typescript-eslint/eslint-plugin": "3.8.0", "@typescript-eslint/parser": "3.8.0", "chai": "^4.2.0", "commitlint-config-jira": "^1.4.1", "commitlint-plugin-jira-rules": "^1.4.0", "env-cmd": "^10.1.0", "eslint": "7.6.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-import": "2.22.0", "husky": "4.2.5", "jest": "^29.7.0", "jest-mock": "^29.7.0", "lint-staged": "10.2.11", "nock": "^13.4.0", "nyc": "^15.0.1", "prettier": "2.0.5", "reflect-metadata": "0.1.13", "release-it": "13.6.6", "sinon": "^9.0.2", "supertest": "^4.0.2", "ts-jest": "^29.1.1", "ts-loader": "^6.2.1", "ts-node": "^8.10.2", "tsconfig-paths": "^3.9.0", "typescript": "^4.9.5"}, "lint-staged": {"*.ts": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}