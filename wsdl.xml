<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:security="http://www.betfair.com/security/" name="BetfairGamingInterface" targetNamespace="http://www.betfair.com/serviceapi/v1.0/BetfairGamingInterface/" xmlns:tns="http://www.betfair.com/servicetypes/v1/BetfairGamingInterface/" xmlns:wns="http://www.betfair.com/serviceapi/v1.0/BetfairGamingInterface/">
    <wsdl:documentation>BetfairGamingInterface.wsdl v1.0</wsdl:documentation>
    <wsdl:types>
        <xsd:schema targetNamespace="http://www.betfair.com/security/" elementFormDefault="qualified">
            <xsd:element name="Credentials">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:any minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
        </xsd:schema>
        <xsd:schema targetNamespace="http://www.betfair.com/servicetypes/v1/BetfairGamingInterface/" elementFormDefault="qualified">
            <xsd:annotation>
                <xsd:documentation>BetfairGamingInterface.wsdl v1.0</xsd:documentation>
            </xsd:annotation>
            <xsd:element name="PlayerLoginRequest" type="tns:PlayerLoginRequestType"/>
            <xsd:complexType name="PlayerLoginRequestType">
                <xsd:all>
                    <xsd:element name="playerLoginRequest" type="tns:PlayerLoginBgiRequestType" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="PlayerLoginResponse" type="tns:PlayerLoginResponseType"/>
            <xsd:complexType name="PlayerLoginResponseType">
                <xsd:all>
                    <xsd:element name="response" type="tns:PlayerLoginBgiResponseType" minOccurs="0" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="GamePlayRequest" type="tns:GamePlayRequestType"/>
            <xsd:complexType name="GamePlayRequestType">
                <xsd:all>
                    <xsd:element name="gamePlayRequest" type="tns:GamePlayBgiRequestType" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="GamePlayResponse" type="tns:GamePlayResponseType"/>
            <xsd:complexType name="GamePlayResponseType">
                <xsd:all>
                    <xsd:element name="response" type="tns:GamePlayBgiResponseType" minOccurs="0" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="RetrieveBalanceRequest" type="tns:RetrieveBalanceRequestType"/>
            <xsd:complexType name="RetrieveBalanceRequestType">
                <xsd:all>
                    <xsd:element name="balanceRequest" type="tns:BalanceBgiRequestType" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="RetrieveBalanceResponse" type="tns:RetrieveBalanceResponseType"/>
            <xsd:complexType name="RetrieveBalanceResponseType">
                <xsd:all>
                    <xsd:element name="response" type="tns:BalanceBgiResponseType" minOccurs="0" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="RetrieveExchangeRatesRequest" type="tns:RetrieveExchangeRatesRequestType"/>
            <xsd:complexType name="RetrieveExchangeRatesRequestType">
                <xsd:all>
                    <xsd:element name="exchangeRatesRequest" type="tns:ExchangeRatesBgiRequestType" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="RetrieveExchangeRatesResponse" type="tns:RetrieveExchangeRatesResponseType"/>
            <xsd:complexType name="RetrieveExchangeRatesResponseType">
                <xsd:all>
                    <xsd:element name="response" type="tns:ExchangeRatesBgiResponseType" minOccurs="0" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="SendClientNotificationRequest" type="tns:SendClientNotificationRequestType"/>
            <xsd:complexType name="SendClientNotificationRequestType">
                <xsd:all>
                    <xsd:element name="clientNotificationRequest" type="tns:ClientNotificationBgiRequestType" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="SendClientNotificationResponse" type="tns:SendClientNotificationResponseType"/>
            <xsd:complexType name="SendClientNotificationResponseType">
                <xsd:all>
                    <xsd:element name="response" type="tns:ClientNotificationBgiResponseType" minOccurs="0" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="WalletTransferRequest" type="tns:WalletTransferRequestType"/>
            <xsd:complexType name="WalletTransferRequestType">
                <xsd:all>
                    <xsd:element name="walletTransferRequest" type="tns:WalletTransferBgiRequestType" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="WalletTransferResponse" type="tns:WalletTransferResponseType"/>
            <xsd:complexType name="WalletTransferResponseType">
                <xsd:all>
                    <xsd:element name="response" type="tns:WalletTransferBgiResponseType" minOccurs="0" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="BaseRequestType">
                <xsd:all>
                    <xsd:element name="rgsAuthentication" type="tns:RgsAuthenticationType" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="userAuthentication" type="tns:UserAuthenticationType" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="requestId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="timestamp" type="xsd:long" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="BaseResponseType">
                <xsd:all>
                    <xsd:element name="responseId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="timestamp" type="xsd:long" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="PlayerLoginBgiRequestType">
                <xsd:all>
                    <xsd:element name="baseRequest" type="tns:BaseRequestType" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="productName" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="gameCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="ipAddress" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="channel" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="realMode" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                    <!--map type-->
                    <xsd:element name="metadata" minOccurs="0" maxOccurs="1">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="entry" minOccurs="0" maxOccurs="unbounded">
                                    <xsd:complexType>
                                        <xsd:sequence>
                                            <xsd:element name="String" type="xsd:string" minOccurs="0"/>
                                        </xsd:sequence>
                                        <xsd:attribute name="key" type="xsd:string"/>
                                    </xsd:complexType>
                                </xsd:element>
                            </xsd:sequence>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="PlayerLoginBgiResponseType">
                <xsd:all>
                    <xsd:element name="baseResponse" type="tns:BaseResponseType" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="currency" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="balance" type="tns:BalanceType" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="countryOfResidence" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="gameConfiguration" type="tns:GameConfigurationType" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="valueTier" type="xsd:int" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="GamePlayBgiRequestType">
                <xsd:all>
                    <xsd:element name="baseRequest" type="tns:BaseRequestType" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="productName" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="gameCode" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="playType" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="amount" type="xsd:long" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="jackpotId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="jackpotAmount" type="xsd:long" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="jackpotInfo">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="JackpotInfo" type="tns:JackpotInfoType" minOccurs="0" maxOccurs="unbounded"/>
                            </xsd:sequence>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="gameRoundId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="gamePlayId" type="xsd:long" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="gamePlayDescription" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="gameRoundCompleted" type="xsd:boolean" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="transactionId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="channel" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <!--map type-->
                    <xsd:element name="context" minOccurs="0" maxOccurs="1">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="entry" minOccurs="0" maxOccurs="unbounded">
                                    <xsd:complexType>
                                        <xsd:sequence>
                                            <xsd:element name="String" type="xsd:string" minOccurs="0"/>
                                        </xsd:sequence>
                                        <xsd:attribute name="key" type="xsd:string"/>
                                    </xsd:complexType>
                                </xsd:element>
                            </xsd:sequence>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="GamePlayBgiResponseType">
                <xsd:all>
                    <xsd:element name="baseResponse" type="tns:BaseResponseType" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="transferId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="balance" type="tns:BalanceType" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="operatedRealAmount" type="xsd:long" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="operatedBonusAmount" type="xsd:long" minOccurs="0" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="BalanceBgiRequestType">
                <xsd:all>
                    <xsd:element name="baseRequest" type="tns:BaseRequestType" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="productName" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="gameCode" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="BalanceBgiResponseType">
                <xsd:all>
                    <xsd:element name="baseResponse" type="tns:BaseResponseType" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="balance" type="tns:BalanceType" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="ExchangeRatesBgiRequestType">
                <xsd:all>
                    <xsd:element name="baseRequest" type="tns:BaseRequestType" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="ExchangeRatesBgiResponseType">
                <xsd:all>
                    <xsd:element name="baseResponse" type="tns:BaseResponseType" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="exchangeRates">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="ExchangeRate" type="tns:ExchangeRateType" minOccurs="1" maxOccurs="unbounded"/>
                            </xsd:sequence>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="ExchangeRateType">
                <xsd:all>
                    <xsd:element name="baseCurrency" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="currency" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="factor" type="xsd:double" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="ClientNotificationBgiRequestType">
                <xsd:all>
                    <xsd:element name="baseRequest" type="tns:BaseRequestType" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="productName" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="gameCode" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="notificationName" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <!--map type-->
                    <xsd:element name="notificationData" minOccurs="1" maxOccurs="1">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="entry" minOccurs="0" maxOccurs="unbounded">
                                    <xsd:complexType>
                                        <xsd:sequence>
                                            <xsd:element name="String" type="xsd:string" minOccurs="0"/>
                                        </xsd:sequence>
                                        <xsd:attribute name="key" type="xsd:string"/>
                                    </xsd:complexType>
                                </xsd:element>
                            </xsd:sequence>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="ClientNotificationBgiResponseType">
                <xsd:all>
                    <xsd:element name="baseResponse" type="tns:BaseResponseType" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="notificationResponse" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="notificationResponseData" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="WalletTransferBgiRequestType">
                <xsd:all>
                    <xsd:element name="baseRequest" type="tns:BaseRequestType" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="productName" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="amount" type="xsd:long" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="transferId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="transferDescription" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="transferType" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="operationType" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <!--map type-->
                    <xsd:element name="context" minOccurs="0" maxOccurs="1">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="entry" minOccurs="0" maxOccurs="unbounded">
                                    <xsd:complexType>
                                        <xsd:sequence>
                                            <xsd:element name="String" type="xsd:string" minOccurs="0"/>
                                        </xsd:sequence>
                                        <xsd:attribute name="key" type="xsd:string"/>
                                    </xsd:complexType>
                                </xsd:element>
                            </xsd:sequence>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="WalletTransferBgiResponseType">
                <xsd:all>
                    <xsd:element name="baseResponse" type="tns:BaseResponseType" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="transferId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="balance" type="tns:BalanceType" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="ErrorResponseType">
                <xsd:all>
                    <xsd:element name="errorCode" type="xsd:int" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="errorDescription" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="RgsAuthenticationType">
                <xsd:all>
                    <xsd:element name="rgsUser" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="rgsPassword" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="UserAuthenticationType">
                <xsd:all>
                    <xsd:element name="accountId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="token" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="authenticationType" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="BalanceType">
                <xsd:all>
                    <xsd:element name="totalAmount" type="xsd:long" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="mainAmount" type="xsd:long" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="bonusAmount" type="xsd:long" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="GameConfigurationType">
                <xsd:all>
                    <xsd:element name="minStake" type="xsd:long" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="maxStake" type="xsd:long" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="defaultStake" type="xsd:long" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="maxWinnings" type="xsd:long" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="stakeIncrement">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="Long" type="xsd:long" minOccurs="0" maxOccurs="unbounded"/>
                            </xsd:sequence>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="promoBan" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="JackpotInfoType">
                <xsd:all>
                    <xsd:element name="jackpotId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="jackpotAmount" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="jackpotTransferType" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
            <xsd:element name="BgiException" type="tns:BgiExceptionType"/>
            <xsd:complexType name="BgiExceptionType">
                <xsd:all>
                    <xsd:element name="errorCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="message" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="errorParameters" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                </xsd:all>
            </xsd:complexType>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="HeadersInOut">
        <wsdl:part name="credentials" element="security:Credentials"/>
    </wsdl:message>
    <wsdl:message name="PlayerLoginIn">
        <wsdl:part name="parameters" element="tns:PlayerLoginRequest"/>
    </wsdl:message>
    <wsdl:message name="PlayerLoginOut">
        <wsdl:part name="parameters" element="tns:PlayerLoginResponse"/>
    </wsdl:message>
    <wsdl:message name="GamePlayIn">
        <wsdl:part name="parameters" element="tns:GamePlayRequest"/>
    </wsdl:message>
    <wsdl:message name="GamePlayOut">
        <wsdl:part name="parameters" element="tns:GamePlayResponse"/>
    </wsdl:message>
    <wsdl:message name="RetrieveBalanceIn">
        <wsdl:part name="parameters" element="tns:RetrieveBalanceRequest"/>
    </wsdl:message>
    <wsdl:message name="RetrieveBalanceOut">
        <wsdl:part name="parameters" element="tns:RetrieveBalanceResponse"/>
    </wsdl:message>
    <wsdl:message name="RetrieveExchangeRatesIn">
        <wsdl:part name="parameters" element="tns:RetrieveExchangeRatesRequest"/>
    </wsdl:message>
    <wsdl:message name="RetrieveExchangeRatesOut">
        <wsdl:part name="parameters" element="tns:RetrieveExchangeRatesResponse"/>
    </wsdl:message>
    <wsdl:message name="SendClientNotificationIn">
        <wsdl:part name="parameters" element="tns:SendClientNotificationRequest"/>
    </wsdl:message>
    <wsdl:message name="SendClientNotificationOut">
        <wsdl:part name="parameters" element="tns:SendClientNotificationResponse"/>
    </wsdl:message>
    <wsdl:message name="WalletTransferIn">
        <wsdl:part name="parameters" element="tns:WalletTransferRequest"/>
    </wsdl:message>
    <wsdl:message name="WalletTransferOut">
        <wsdl:part name="parameters" element="tns:WalletTransferResponse"/>
    </wsdl:message>
    <wsdl:message name="BgiExceptionFault">
        <wsdl:part name="fault" element="tns:BgiException"/>
    </wsdl:message>
    <wsdl:portType name="BetfairGamingInterfaceService">
        <wsdl:operation name="playerLogin">
            <wsdl:input message="wns:PlayerLoginIn"/>
            <wsdl:output message="wns:PlayerLoginOut"/>
            <wsdl:fault name="bgiException" message="wns:BgiExceptionFault"/>
        </wsdl:operation>
        <wsdl:operation name="gamePlay">
            <wsdl:input message="wns:GamePlayIn"/>
            <wsdl:output message="wns:GamePlayOut"/>
            <wsdl:fault name="bgiException" message="wns:BgiExceptionFault"/>
        </wsdl:operation>
        <wsdl:operation name="retrieveBalance">
            <wsdl:input message="wns:RetrieveBalanceIn"/>
            <wsdl:output message="wns:RetrieveBalanceOut"/>
            <wsdl:fault name="bgiException" message="wns:BgiExceptionFault"/>
        </wsdl:operation>
        <wsdl:operation name="retrieveExchangeRates">
            <wsdl:input message="wns:RetrieveExchangeRatesIn"/>
            <wsdl:output message="wns:RetrieveExchangeRatesOut"/>
            <wsdl:fault name="bgiException" message="wns:BgiExceptionFault"/>
        </wsdl:operation>
        <wsdl:operation name="sendClientNotification">
            <wsdl:input message="wns:SendClientNotificationIn"/>
            <wsdl:output message="wns:SendClientNotificationOut"/>
            <wsdl:fault name="bgiException" message="wns:BgiExceptionFault"/>
        </wsdl:operation>
        <wsdl:operation name="walletTransfer">
            <wsdl:input message="wns:WalletTransferIn"/>
            <wsdl:output message="wns:WalletTransferOut"/>
            <wsdl:fault name="bgiException" message="wns:BgiExceptionFault"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="BetfairGamingInterfaceService" type="wns:BetfairGamingInterfaceService">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="playerLogin">
            <soap:operation soapAction="playerLogin" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:output>
            <wsdl:fault name="bgiException">
                <soap:fault use="literal" name="bgiException"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="gamePlay">
            <soap:operation soapAction="gamePlay" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:output>
            <wsdl:fault name="bgiException">
                <soap:fault use="literal" name="bgiException"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="retrieveBalance">
            <soap:operation soapAction="retrieveBalance" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:output>
            <wsdl:fault name="bgiException">
                <soap:fault use="literal" name="bgiException"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="retrieveExchangeRates">
            <soap:operation soapAction="retrieveExchangeRates" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:output>
            <wsdl:fault name="bgiException">
                <soap:fault use="literal" name="bgiException"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="sendClientNotification">
            <soap:operation soapAction="sendClientNotification" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:output>
            <wsdl:fault name="bgiException">
                <soap:fault use="literal" name="bgiException"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="walletTransfer">
            <soap:operation soapAction="walletTransfer" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
                <soap:header message="wns:HeadersInOut" part="credentials" use="literal"/>
            </wsdl:output>
            <wsdl:fault name="bgiException">
                <soap:fault use="literal" name="bgiException"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="BetfairGamingInterfaceService">
        <wsdl:port name="BetfairGamingInterfaceService" binding="wns:BetfairGamingInterfaceService">
            <soap:address location="http://localhost/this-should-be-set-programatically"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>