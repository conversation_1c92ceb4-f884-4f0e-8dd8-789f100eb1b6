// tslint:disable:max-line-length
import { HttpGatewayConfig } from "@skywind-group/sw-integration-core";
import { SoapGatewayConfig } from "./common/intefraces/soap.interface";

const http: HttpGatewayConfig = {
    operatorUrl: process.env.OPERATOR_HTTP_URL || `http://localhost:${process.env.SERVER_OPERATOR_PORT || 6002}`,
    defaultOptions: {
        timeout: +process.env.OPERATOR_HTTP_TIMEOUT || 5000,
        proxy: process.env.OPERATOR_HTTP_PROXY
    },
    keepAlive: {
        maxFreeSockets: +process.env.OPERTOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 100,
        socketActiveTTL: +process.env.OPERTOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 60000,
        freeSocketKeepAliveTimeout: +process.env.OPERTOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 30000
    },
    ssl: {
        ca: process.env.OPERATOR_SSL_CA,
        key: process.env.OPERATOR_SSL_KEY,
        cert: process.env.OPERATOR_SSL_CERT,
        password: process.env.OPERATOR_SSL_PASSWORD
    }
};

const soap: SoapGatewayConfig = {
    envelopeKey: process.env.SOAP_ENVELOP_KEY || "soapenv",
    namespace: process.env.SOAP_NAMESPACE || "bet",
    additionalHeaders: [{ "X-Client": "skywind" }],
    xmlnsAttributesURL:
        process.env.SOAP_XMLNS_ATTRIBUTES || "http://www.betfair.com/serviceapi/v1.0/BetfairGamingInterface/",
    wsdlUrl:
        process.env.SOAP_WSDL_URL || "https://bgi.betfair.com.nxt.ppbdev.com/wsdl/BetfairGamingInterface_v1.0.wsdl",
    url: process.env.SOAP_URL || "https://bgi.betfair.com.nxt.ppbdev.com/BetfairGamingInterfaceService/v1.0",
    logSecureKeys: ["bet:rgsPassword", "bet:token", "x-sw-player-token"],
    secure: {
        cert:
            process.env.SOAP_SECURE_CERT ||
            "-----BEGIN CERTIFICATE-----\n" +
                "MIIEZzCCA0+gAwIBAgIUEXSwheMpPA3S87KD1qrUPVSooLEwDQYJKoZIhvcNAQEL\n" +
                "BQAwgagxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQH\n" +
                "Ew1TYW4gRnJhbmNpc2NvMRkwFwYDVQQKExBDbG91ZGZsYXJlLCBJbmMuMRswGQYD\n" +
                "VQQLExJ3d3cuY2xvdWRmbGFyZS5jb20xNDAyBgNVBAMTK01hbmFnZWQgQ0EgNmU4\n" +
                "MWQzMWI1MjYzOGQ3ODYxZWQ1MDNkOWQ0Yzc2Y2EwHhcNMjMxMDE4MTUxNjAwWhcN\n" +
                "MjYxMDE3MTUxNjAwWjB0MQswCQYDVQQGEwJVSzEUMBIGA1UEChMLRmx1dHRlciBV\n" +
                "S0kxFzAVBgNVBAsTDlVLSSBHYW1pbmcgRGV2MRAwDgYDVQQDEwdza3l3aW5kMSQw\n" +
                "IgYJKoZIhvcNAQkBDBVhbmFtYXJpYS5sdXB1QHBwYi5jb20wggEiMA0GCSqGSIb3\n" +
                "DQEBAQUAA4IBDwAwggEKAoIBAQDd9awJC9rxGAPpusNaw5uJCv0ik5Fbt0Kc68d5\n" +
                "XoWccoQ2LJ0sIfZbWqHxd5fRtRNwKag7ZB15tZUgiYEAIEiEO2XbCekyRtfilvBk\n" +
                "TJuhaJFuYtPQsDh7VZSqTgHMFAi+TZ2RnU6QW2VIMosLSVP/l5jja76SpAtr3+8y\n" +
                "vOiRUKphoAI+8DLKwHWQctzkd1e/8jYXUZiqI/gUduL5dVecXCZgp71dkS5Kk27G\n" +
                "MZ2sgC5wU1lyORRdk/MyK4CPlpus8ejBqdVp+GwT2XZvkd3ZIi4AXF4EwgiKty/M\n" +
                "zqXDyOEbCs2Ibx2P2bRUz5y97thXjxvHL+EEM3dqXAFXZ7pFAgMBAAGjgbswgbgw\n" +
                "EwYDVR0lBAwwCgYIKwYBBQUHAwIwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQUjLuZ\n" +
                "3Kkj1VLtAtME6E3H3M/tbKYwHwYDVR0jBBgwFoAUb6WAK2xdoKxqEwUS2GKdTGFM\n" +
                "/HMwUwYDVR0fBEwwSjBIoEagRIZCaHR0cDovL2NybC5jbG91ZGZsYXJlLmNvbS9l\n" +
                "MDcyNjg2Yi1hNmE0LTQ4ZTktOWJiZS0xNzMyOWJjMjAyNDYuY3JsMA0GCSqGSIb3\n" +
                "DQEBCwUAA4IBAQCuos2LXLGSs4YMizLscOsqca9POTONhMC1L9PuUZlUrhAXkRBs\n" +
                "qN3h4hjBOkSWX/AkNNX1L+9yqbknwQfFE6ZgFY6BwDVvvpW3ZEWUQbORaUTdF/0n\n" +
                "V7Fm2UYu9YoF9Xy4knJvpPk/CB5Te68BIrl8mEu4yWjzUKP1xSCPtD3dOYW2tDd9\n" +
                "sMKHMZGqixUVFjj+b08t92v0XV4A24cSw2XAQg8w9TIbiFk0LtJwGrexDhUwRCoH\n" +
                "Cc3uATA6/Bl4vH0OeJYV/oNmn1jw4V2AwVawXUKDg3H+xscpexjweQAHoiUSLBjt\n" +
                "k2RQO3oxR23nVATD7bekK2XK9MDMezralLhp\n" +
                "-----END CERTIFICATE-----",
        key:
            process.env.SOAP_SECURE_KEY ||
            "-----BEGIN RSA PRIVATE KEY-----\n" +
                "Proc-Type: 4,ENCRYPTED\n" +
                "DEK-Info: DES-EDE3-CBC,DCB57FC39B6BD981\n" +
                "\n" +
                "EU8sdj+IzezWbpx46V6ojbsI9cbvSieQQepY2k0hsXXc9rffYZuNp2vLOQa03r0f\n" +
                "YciIVxOtGI3KOaNb+pNEGt4bvdffaGBK4Vw1xy1rQjjNCs2faluPAFIAY9doS6+s\n" +
                "R7893d2kzia+n4G9qtAa+p51heyawTICE+xwruJ8n34irDEnZOA6eVwWHXGYIXJw\n" +
                "n5R+514ZkqCJ2q4LnVLOT9FaOpWq7VWNZj0YsfKbY0Wr+mHt/m1tWUHMx6de4VjM\n" +
                "NY6Lc1F6fVSh9uvdjJ3XIoIEJjAbuMapLLdCCGdHL+Ioc/Xs1TzGeutmFseRHEIa\n" +
                "gyonaFBw2sKQ/Dep2FzDO+uhd4dmyG0Rr85dSLrEDKNzrordGx79fzh+w7YxvMMD\n" +
                "BlgUxqsM/oG2kEcchi0DVl83ZLj6T4u+PH22Y9MhZKEL5UUqb8JkpAOEKhTSkwgm\n" +
                "x7wwYzxHUZ8n/KwbDwLDSKotLcArreliTHVrQSX2sYEhAK3LbPaUzlXB1FJSyvPZ\n" +
                "JdU/B0OJgQW60AMtFbF3UnD7nEawrxawag0cL2kAQfwu/d3YtnixDmxboH2IEIr9\n" +
                "LNR/a6Ax6Vu7/bt3egrF9qXBU+rQ3WhzGeSxgnlp2VWe915som3LdKLoevIBTuDu\n" +
                "otY+DUvOm1aExex346k2p+J0CK2rC7DFeGEaLVN3Qgk77JnQIQSdhqXnxzEX/+BF\n" +
                "GTRurqkOeM5N7hzHwcTIW5Es8Vu2HYMyK2vOf9kDhqYp6zQgZp9R1SejzRTwe8mZ\n" +
                "2etvEx8uNGrxyM9STOTO1qmdrylLza08fDOBAzWeOCS5ByxIg56UzUZ+Ggouats2\n" +
                "D5BQTIBdaYDwSaJ1reFVEoHGmt7ehxHnwFkJHepw2nwELIreaX4LMI45S75Id0KF\n" +
                "Oh9exmx4Z+ceH7u5mH9VoicIjwK+KhsrdMZZ23oUoMSmrnyushOO/k+nIfeBuCkU\n" +
                "D/4Jx5lDlM9szbkPpHujvQvQ+37VwWWqrGNdt49VE4nYkgckPyjpQDZYKm/058To\n" +
                "SPnJkK0azc9bk//ZkU6izxlEwbOUCeup9Sab5UMlzbJ2qeDjuQye5kYKlQdGbnj0\n" +
                "5INWG5kntzSzLjEPeDh0kyjxi+2FNb4NWVKvWusWQPZV1QHXxsfaeZEYVRal2gkh\n" +
                "blp0KtnZQXRzdq/qRcQlXUPGeHjXkBzsQcZs1immqFDc9/WHogZFCG0CEcSxIO2p\n" +
                "PEDDISutqrOqUb9XSZtiAhcWZjgVLyd+72yz8zHkfTUlwX5IKh1QJ2yVNOoW9aV/\n" +
                "rtaP+YiTuHIN2kdQeE5Ku48lhwyC6Lqv83ksAfHtJ/CylOz6ZIMjBLQvheKDmweK\n" +
                "+qrJIoXfJ+oUFKINRiRXJqCcH/Hg01wt/cXJzvCvgrqf7dIWIBLDi7VN9GuPMpfX\n" +
                "LmG1FmMxHZxtYJ66uHVS3VgLEXkKyces27pDBQfnfjPxjGtZKKX5VSuL0ivu2xgO\n" +
                "9vZwhHvL4sqBgrIK5cI7xCG8tz9IF4ckawf/EmmUQV2FZnqAugVBec/vgp4e6oje\n" +
                "Br9urwYx/Ntwbh2UncWvU4km+7UIUl9brQCc8NnUSDVGpFnclDAyVyyPhUvbW2e7\n" +
                "-----END RSA PRIVATE KEY-----",
        passphrase: process.env.SOAP_SECURE_PASSPHRASE || "howmanybets"
    }
};

const config = {
    environment: process.env.NODE_ENV || "development",
    merchantType: process.env.MERCHANT_TYPE || "betfair",
    operatorAPIBaseUrl: process.env.MAPI_SERVER_URL || "http://localhost:3000",
    internalMAPIUrl: process.env.MAPI_INTERNAL_URL || "http://localhost:4004",

    region: process.env.REGION_TYPE || "default",

    server: {
        walletPort: +process.env.SERVER_WALLET_PORT || 6000,
        launcherPort: +process.env.SERVER_LAUNCHER_PORT || 6001,
        mockPort: +process.env.SERVER_MOCK_PORT || 6003
    },

    internalServer: {
        wallet: +process.env.INTERNAL_SERVER_WALLET_PORT || 4054,
        launcher: +process.env.INTERNAL_SERVER_LAUNCHER_PORT || 4055,
        mock: +process.env.INTERNAL_SERVER_MOCK_PORT || 4056,
        api: {
            isEnabled: process.env.INTERNAL_API === "true"
        }
    },

    http,
    soap,

    operator: {
        name: "BetFair",
        secretKey: process.env.OPERATOR_SECRET_KEY || "5FbzFNDHYRghixCwd8NTNJn9Tq4cl1Ue",
        licenseId: +process.env.OPERATOR_LICENSE_ID || 134,
        cryptoAlgorythm: process.env.OPERATOR_CRYPTO_ALG || "sha256",
        offlineTokens: {
            settlement: process.env.OPERATOR_OFFLINE_TOKEN_SETTLEMENT || "SkyWind_SETTLEMENT",
            bonusSettlement: process.env.OPERATOR_OFFLINE_TOKEN_BONUS_SETTLEMENT || "SkyWind_BONUS_SETTLEMENT",
            cancel: process.env.OPERATOR_OFFLINE_TOKEN_CANCEL || "SkyWind_CANCEL"
        }
    },

    precision: +process.env.BETFAIR_TRANSACTION_PRECISION || 3,
    isMockWallet: process.env.IS_WALLET_MOCK === "true"
};

export default config;
