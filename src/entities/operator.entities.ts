import { MerchantGameInitRequest, MerchantGameTokenData, MerchantGameURLInfo, MerchantStartGameTokenData, PaymentRequest } from "@skywind-group/sw-wallet-adapter-core";
import { CommitBonusPaymentRequest } from "@skywind-group/sw-integration-core";
import { IsNotEmpty, IsOptional, ValidateIf } from "class-validator";

export class IntegrationGameLaunchRequest {
    @IsOptional()
    @IsNotEmpty()
    merchantType?: string // Optional parameter used for testing

    @IsOptional()
    @IsNotEmpty()
    merchantCode?: string // Optional parameter used for testing

    @IsNotEmpty()
    trader: string;

    @IsNotEmpty()
    gameId: string;

    @IsNotEmpty()
    demo?: boolean;

    @ValidateIf(o => o.demo === false)
    @IsNotEmpty()
    token?: string;

    @ValidateIf(o => o.demo === false)
    @IsNotEmpty()
    customer?: string;

    @ValidateIf(o => o.demo === false)
    @IsNotEmpty()
    currency?: string;

    @IsNotEmpty()
    lang: string;

    @IsNotEmpty()
    country: string;

    @IsNotEmpty()
    platform: string;

    @IsOptional()
    lobby?: string;
    
    @IsOptional()
    tableId?: string;
}

export interface IntegrationGameTokenData extends MerchantGameTokenData {
    customer: string;
    token: string;
    gameId: string;
    currency: string;
    demo: boolean;
    platform: string;
    lang?: string;
    lobby?: string;
    tableId?: string;
    trader?: string;
    country?: string;
    language?: string;
}

export interface IntegrationInitRequest extends MerchantGameInitRequest {
    customer?: string;
    country: string;
    currency: string;
    platform: "desktop" | "mobile";
}

export interface IntegrationStartGameTokenData extends MerchantStartGameTokenData {
    token?: string;
}

export interface IntegrationMerchantGameURLInfo {
    urlParams: MerchantGameURLInfo["urlParams"];
    tokenData: IntegrationStartGameTokenData;
}

export interface IntegrationPaymentRequest extends CommitBonusPaymentRequest<IntegrationGameTokenData> {
    request: PaymentRequest;
    operatorRoundId?: string;
}

export interface OperatorFreespinData {
    freespinRef: string;
    requested?: boolean;
    remainingRounds?: number;
    totalWinnings?: number;
}

export interface OperatorPromoData {
    promoType: OperatorPromoType;
    promoRef: string;
    freeSpinData?: OperatorFreespinData;
}

export enum OperatorPromoType {
    FSW = "FSW", // freespin
    JPW = "JPW", // jackpot
    CB = "CB",   // cashBack
    TW = "TW",   // tournament win
    RW = "RW",   // reward
    REW = "REW", // red envelope win
    CDW = "CDW", // cash drop win
    RB = "RB"    // rakeBack
}

export interface OperatorBaseRequest {
    customer: string;
    token: string;
    hash?: string;
}

export interface OperatorAuthRequest extends OperatorBaseRequest {
}

export interface OperatorDebitRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    tip?: boolean;
}

export interface OperatorCreditRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    freespin?: OperatorFreespinData;
}

export interface OperatorDebitCreditRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    creditAmount: number;
    currency: string;
    betId: string;
    trxId: string;
    creditTrxId: string;
    tip?: boolean;
}

export interface OperatorRollbackRequest extends OperatorBaseRequest {
    gameId: string;
    trxId: string;
}

export interface OperatorPromoRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    promo: OperatorPromoData;
}

export interface OperatorBaseResponse {
    code: number;
    status: string;
    currency: string;
    balance: number;
    bonusBalance: number;
}

export interface OperatorAuthResponse extends OperatorBaseResponse {
    traderId?: number;
}

export interface OperatorDebitResponse extends OperatorBaseResponse {
    trxId: number;
}

export interface OperatorCreditResponse extends OperatorBaseResponse {
    trxId?: number;
}

export interface OperatorDebitCreditResponse extends OperatorBaseResponse {
    trxId: number;
    creditTrxId?: number;
}

export interface OperatorRollbackResponse extends OperatorBaseResponse {
    trxId: number;
}

export interface OperatorPromoResponse extends OperatorBaseResponse {
    trxId: number;
}

export interface OperatorHistoryRequest {
    customer: string;
    roundId: string;
    gameId: string;
    lang?: string;
    trader?: string;
}

export interface OperatorRoundDetailsResponse {
    code: number;
    status: string;
    json?: any;
    image?: {
        url: string;
        width?: number;
        height?: number;
    };
    html?: string;
}

export interface OperatorError {
    code: number;
    status: string;
}

export const operatorErrorCodes = {
    SUCCESS: 0,
    UNKNOWN_ERROR: 1,
    INTERNAL_CACHE_ERROR: 2,
    DATA_OUT_OF_RANGE: 3,
    UNAUTHORIZED_REQUEST: 4,
    NOT_INTEGRATED: 5,
    TOKEN_CUSTOMER_MISMATCH: 6,
    UNSUPPORTED_API_VERSION: 7,
    PROMOTION_TYPE_NOT_SUPPORTED: 8,
    BET_RECORD_NOT_FOUND: 9,
    TRANSACTION_NOT_FOUND: 10,
    BET_ALREADY_WON: 11,
    BET_ALREADY_SETTLED: 12,
    AUTHENTICATION_FAILED: 13,
    GAME_NOT_FOUND: 14,
    INVALID_GAME: 15,
    BET_LIMIT_REACHED: 16,
    LOSS_LIMIT_REACHED: 17,
    SESSION_LIMIT_REACHED: 18,
    PROFIT_LIMIT_REACHED: 19,
    INVALID_CASINO_VENDOR: 20,
    ALL_BET_ARE_OFF: 21,
    CUSTOMER_NOT_FOUND: 22,
    INVALID_CURRENCY: 23,
    INSUFFICIENT_FUNDS: 24,
    PLAYER_SUSPENDED: 25,
    REQUIRED_FIELD_MISSING: 26,
    TOKEN_NOT_FOUND: 27,
    TOKEN_TIMEOUT: 28,
    TOKEN_INVALID: 29,
    NEGATIVE_DEPOSIT: 30,
    NEGATIVE_WITHDRAWAL: 31
} as const;

export const operatorStatusMessages = {
    [operatorErrorCodes.SUCCESS]: "Success",
    [operatorErrorCodes.UNKNOWN_ERROR]: "Unknown error",
    [operatorErrorCodes.INTERNAL_CACHE_ERROR]: "Internal cache error",
    [operatorErrorCodes.DATA_OUT_OF_RANGE]: "Data out of range",
    [operatorErrorCodes.UNAUTHORIZED_REQUEST]: "Unauthorized request",
    [operatorErrorCodes.NOT_INTEGRATED]: "Vendor not integrated",
    [operatorErrorCodes.TOKEN_CUSTOMER_MISMATCH]: "Token customer mismatch",
    [operatorErrorCodes.UNSUPPORTED_API_VERSION]: "Unsupported API version",
    [operatorErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED]: "Promotion type not supported",
    [operatorErrorCodes.BET_RECORD_NOT_FOUND]: "Bet record not found",
    [operatorErrorCodes.TRANSACTION_NOT_FOUND]: "Transaction not found",
    [operatorErrorCodes.BET_ALREADY_WON]: "Bet already won",
    [operatorErrorCodes.BET_ALREADY_SETTLED]: "Bet already settled",
    [operatorErrorCodes.AUTHENTICATION_FAILED]: "Authentication failed",
    [operatorErrorCodes.GAME_NOT_FOUND]: "Game not found",
    [operatorErrorCodes.INVALID_GAME]: "Invalid game",
    [operatorErrorCodes.BET_LIMIT_REACHED]: "Bet limit reached",
    [operatorErrorCodes.LOSS_LIMIT_REACHED]: "Loss limit reached",
    [operatorErrorCodes.SESSION_LIMIT_REACHED]: "Session limit reached",
    [operatorErrorCodes.PROFIT_LIMIT_REACHED]: "Profit limit reached",
    [operatorErrorCodes.INVALID_CASINO_VENDOR]: "Invalid casino vendor",
    [operatorErrorCodes.ALL_BET_ARE_OFF]: "All bets are off",
    [operatorErrorCodes.CUSTOMER_NOT_FOUND]: "Customer not found",
    [operatorErrorCodes.INVALID_CURRENCY]: "Invalid currency",
    [operatorErrorCodes.INSUFFICIENT_FUNDS]: "Insufficient funds",
    [operatorErrorCodes.PLAYER_SUSPENDED]: "Player suspended",
    [operatorErrorCodes.REQUIRED_FIELD_MISSING]: "Required field missing",
    [operatorErrorCodes.TOKEN_NOT_FOUND]: "Token not found",
    [operatorErrorCodes.TOKEN_TIMEOUT]: "Token timeout",
    [operatorErrorCodes.TOKEN_INVALID]: "Token invalid",
    [operatorErrorCodes.NEGATIVE_DEPOSIT]: "Negative deposit",
    [operatorErrorCodes.NEGATIVE_WITHDRAWAL]: "Negative withdrawal"
} as const;
