import {
    Balance,
    MerchantGameTokenData,
    MerchantIn<PERSON>,
    MerchantStartGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import { v4 as uuidv4 } from "uuid";

interface IntegrationSpecificTokenData {
    rgsUser?: string;
    rgsPassword?: number;
    accountId?: string;
    token?: string;
    authenticationType?: "ONLINE" | "OFFLINE";
    requestId?: string;
    timestamp?: string;
    productName?: string;
    platform?: string;
    jurisdiction?: string;
}

export interface IntegrationStartGameTokenData extends MerchantStartGameTokenData, IntegrationSpecificTokenData {}

export interface IntegrationGameTokenData extends MerchantGameTokenData, IntegrationSpecificTokenData {}

export enum OPERATOR_REQUEST_COMMAND {
    BET = "BET",
    BONUS_BET = "BONUS_BET",
    WIN = "SETTLEMENT",
    BOUNS_WIN = "BONUS_SETTLEMENT",
    CANCEL = "CANCEL"
}

export interface OperatorBaseRequestPayload {
    rgsAuthentication: {
        rgsUser: string;
        rgsPassword: string;
    };
    userAuthentication: {
        accountId: string;
        token: string;
        authenticationType: string;
    };
    requestId: string;
    timestamp: string;
}

interface OperatorBaseRequest {
    baseRequest: OperatorBaseRequestPayload;
    productName: string;
    gameCode: string;
}

export interface OperatorRetrieveBalancePayload {
    balanceRequest: OperatorBaseRequest;
}

export interface OperatorGamePlayPayload {
    gamePlayRequest: OperatorGamePlayRequest;
}

export interface OperatorGamePlayRequest extends OperatorBaseRequest {
    channel?: string;
    gameRoundId: string;
    gamePlayId: number;
    gamePlayDescription?: string;
    gameRoundCompleted: boolean;
    transactionId: string;
    playType: OPERATOR_REQUEST_COMMAND;
    amount: number;
    jackpotAmount?: number;
    jackpotId?: string;
    jackpotInfo?: OperatorJackpotInfo[];
}

export interface OperatorJackpotInfo {
    jackpotId: string;
    jackpotAmount: number;
    jackpotTransferType: string;
}

export interface OperatorBaseResponseObject {
    responseId: string;
    timestamp: string;
}

export interface OperatorBalanceObject {
    totalAmount?: number;
    mainAmount: number;
    bonusAmount?: number;
}

export interface OperatorBalanceResponse {
    baseResponse: OperatorBaseResponseObject;
    balance: OperatorBalanceObject;
}

export interface OperatorGamePlayResponse extends OperatorBalanceResponse {
    transferId: string;
}

export function createBaseRequest(
    gameToken: IntegrationGameTokenData,
    merchantInfo: MerchantInfo
): OperatorBaseRequest {
    return {
        baseRequest: {
            rgsAuthentication: {
                rgsUser: merchantInfo.params.username,
                rgsPassword: merchantInfo.params.password
            },
            userAuthentication: {
                accountId: gameToken.playerCode,
                token: gameToken.token,
                authenticationType: gameToken.authenticationType ?? "ONLINE"
            },
            requestId: uuidv4(),
            timestamp: Date.now().toString()
        },
        productName: gameToken.productName,
        gameCode: gameToken.gameCode
    };
}

/*
 * This method is used to get the playId for the corresponding event.
 * Betfair has consiquentive playIds for bet and win events
 */
export function getCorrespondingPlayId(roundId: string, eventId: number, type: OPERATOR_REQUEST_COMMAND): number {
    switch (type) {
        case OPERATOR_REQUEST_COMMAND.BET:
        case OPERATOR_REQUEST_COMMAND.BONUS_BET:
        case OPERATOR_REQUEST_COMMAND.CANCEL:
            return +roundId * 2 + eventId * 2;
        case OPERATOR_REQUEST_COMMAND.WIN:
        case OPERATOR_REQUEST_COMMAND.BOUNS_WIN:
            return +roundId * 2 + eventId * 2 + 1;
    }
}

export const UNKNOWN_BALANCE: Balance = { main: 0 };
