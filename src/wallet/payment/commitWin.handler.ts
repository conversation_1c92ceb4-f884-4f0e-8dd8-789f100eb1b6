import { CommitPaymentRequest } from "@skywind-group/sw-integration-core";
import {
    createBaseRequest,
    getCorrespondingPlayId,
    IntegrationGameTokenData,
    OPERATOR_REQUEST_COMMAND,
    OperatorGamePlayPayload,
    OperatorGamePlayRequest,
    OperatorGamePlayResponse
} from "../models";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { BetfairJPTransactionType } from "../../common/intefraces/betfair.interface";
import { convertToBetfairAmount, fromBetfairBalance } from "../../common/utils/calculation";
import { SoapHandler } from "../../common/services/soap.service";

export class CommitWinHandler
    implements
        SoapHandler<
            CommitPaymentRequest<IntegrationGameTokenData>,
            OperatorGamePlayPayload,
            OperatorGamePlayResponse,
            Balance
        > {
    public readonly description = OPERATOR_REQUEST_COMMAND.WIN;

    public build({ gameTokenData, merchantInfo, request }: CommitPaymentRequest<IntegrationGameTokenData>) {
        const gamePlayRequest: OperatorGamePlayRequest = {
            ...createBaseRequest(gameTokenData, merchantInfo),
            channel: request.deviceId.toUpperCase(),
            gameRoundId: request.roundId,
            gamePlayId: getCorrespondingPlayId(request.roundId, request.eventId, OPERATOR_REQUEST_COMMAND.WIN),
            gamePlayDescription: "Normal win",
            gameRoundCompleted: request.roundEnded,
            transactionId: request.transactionId.publicId + "_win",
            playType: OPERATOR_REQUEST_COMMAND.WIN,
            amount: convertToBetfairAmount(request.win) // TODO: make it thru currency conversion,
        };
        if (request.isJPWin) {
            // You can think this is strange shape of the object, but it's fits with spread operator below
            gamePlayRequest.jackpotInfo = [
                {
                    jackpotAmount: request.totalJpWin,
                    jackpotId: Object.keys(request.jackpotWinDetails).shift(),
                    jackpotTransferType: BetfairJPTransactionType.WIN
                },
                // TODO: check if we need to support multiple contributions separately
                // Would be actual for phase 2 (slots)
                // Need to explicitly ask BF how they prefer to receive contributions (and wins)
                {
                    jackpotAmount: request.totalJpContribution,
                    jackpotId: Object.keys(request.jackpotWinDetails).shift(),
                    jackpotTransferType: BetfairJPTransactionType.CONTRIBUTION
                }
            ];
        }
        return { gamePlayRequest };
    }

    public parse(res: OperatorGamePlayResponse, req: CommitPaymentRequest<IntegrationGameTokenData>) {
        return fromBetfairBalance(req.gameTokenData.currency, res.balance, -req.request.win);
    }
}
