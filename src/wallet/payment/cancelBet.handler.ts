import { CommitPaymentRequest } from "@skywind-group/sw-integration-core";
import {
    createBaseRequest,
    getCorrespondingPlayId,
    IntegrationGameTokenData,
    OPERATOR_REQUEST_COMMAND,
    OperatorGamePlayPayload,
    OperatorGamePlayRequest,
    OperatorGamePlayResponse
} from "../models";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { convertToBetfairAmount, fromBetfairBalance } from "../../common/utils/calculation";
import { SoapHandler } from "../../common/services/soap.service";

export class CancelBetHandler
    implements
        SoapHandler<
            CommitPaymentRequest<IntegrationGameTokenData>,
            OperatorGamePlayPayload,
            OperatorGamePlayResponse,
            Balance
        > {
    public readonly description = OPERATOR_REQUEST_COMMAND.CANCEL;

    public build({ gameTokenData, merchantInfo, request }: CommitPaymentRequest<IntegrationGameTokenData>) {
        const gamePlayRequest: OperatorGamePlayRequest = {
            ...createBaseRequest(gameTokenData, merchantInfo),
            playType: OPERATOR_REQUEST_COMMAND.CANCEL,
            amount: convertToBetfairAmount(request.bet),
            gameRoundId: request.roundId,
            gamePlayId: getCorrespondingPlayId(request.roundId, request.eventId, OPERATOR_REQUEST_COMMAND.CANCEL),
            gamePlayDescription: "Cancel Bet",
            gameRoundCompleted: false,
            transactionId: request.transactionId.publicId + "_bet"
        };
        //TODO: double check what should be sent on cancel
        if (request.isJPWin) {
            gamePlayRequest.jackpotAmount = request.totalJpWin;
        }
        return { gamePlayRequest };
    }

    public parse(res: OperatorGamePlayResponse, req: CommitPaymentRequest<IntegrationGameTokenData>) {
        return fromBetfairBalance(req.gameTokenData.currency, res.balance, 0);
    }
}
