import { Inject, Injectable } from "@nestjs/common";
import {
    BalanceRequest,
    BalanceSupport,
    BrokenGameRequest,
    BrokenGameSupport,
    CommitPaymentRequest,
    RefundBetSupport,
    SplitPaymentSupport
} from "@skywind-group/sw-integration-core";
import {
    IntegrationGameTokenData,
    OperatorGamePlayPayload,
    OperatorGamePlayResponse,
    OperatorRetrieveBalancePayload,
    UNKNOWN_BALANCE
} from "../models";
import {
    Balance,
    Balances,
    BrandFinalizationType,
    interruptSocketForLiveGame,
    RequireRefundBetError
} from "@skywind-group/sw-wallet-adapter-core";
import { SoapGateway, SoapHandler } from "../../common/services/soap.service";
import { Names } from "../../names";
import { GetBalanceHandler } from "./balance.handler";
import { CommitBetHandler } from "./commitBet.handler";
import { MerchantInternalRollbackAndRetryError } from "../../common/errors/sw.errors";
import config from "../../config";
import { CommitWinHandler } from "./commitWin.handler";
import { CancelBetHandler } from "./cancelBet.handler";
import { measures } from "@skywind-group/sw-utils";
import measure = measures.measure;

@Injectable()
export class PaymentService
    implements
        SplitPaymentSupport<IntegrationGameTokenData>,
        BalanceSupport<IntegrationGameTokenData>,
        RefundBetSupport<IntegrationGameTokenData>,
        BrokenGameSupport<IntegrationGameTokenData> {
    constructor(@Inject(Names.SoapGateway) protected readonly soapGateway: SoapGateway) {}

    public async getBalances(req: BalanceRequest<IntegrationGameTokenData>): Promise<Balances> {
        return {
            [req.gameTokenData.currency]: await this.retrieveBalance(req)
        };
    }

    @measure({ name: "PaymentService.commitBetPayment", isAsync: true })
    public async commitBetPayment(req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<Balance> {
        if (!Boolean(req.request.bet) || Boolean(req.request.finalizationType)) {
            return this.getBalance(req);
        }
        try {
            return await this.gamePlay(req, new CommitBetHandler(), req.gameTokenData.token);
        } catch (err) {
            this.requireRefundBet(err);
            throw err;
        }
    }

    @measure({ name: "PaymentService.refundBetPayment", isAsync: true })
    public async refundBetPayment(req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<Balance> {
        if (!Boolean(req.request.bet) || Boolean(req.request.finalizationType)) {
            return this.getBalance(req);
        }
        const token = req.gameTokenData.token;
        req.gameTokenData.authenticationType = "OFFLINE";
        req.gameTokenData.token = config.operator.offlineTokens.cancel;
        try {
            return await this.gamePlay(req, new CancelBetHandler(), token);
        } catch (err) {
            interruptSocketForLiveGame(req.request, err, req.gameTokenData);
            this.requireRefundBet(err);
            throw err;
        }
    }

    @measure({ name: "PaymentService.commitWinPayment", isAsync: true })
    public async commitWinPayment(req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<Balance> {
        if ((!Boolean(req.request.win) && !req.request.roundEnded) || Boolean(req.request.finalizationType)) {
            return this.getBalance(req);
        }
        const token = req.gameTokenData.token;
        const retried = req.request.offlineRetry || req.request.retry > 0;
        if (retried) {
            req.gameTokenData.authenticationType = "OFFLINE";
            req.gameTokenData.token = config.operator.offlineTokens.settlement;
        }
        try {
            return await this.gamePlay(req, new CommitWinHandler(), token);
        } catch (err) {
            interruptSocketForLiveGame(req.request, err, req.gameTokenData);
            throw err;
        }
    }

    @measure({ name: "PaymentService.finalizeWithOfflinePayments", isAsync: true })
    public async finalizeWithOfflinePayments(req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<Balance> {
        req.gameTokenData.authenticationType = "OFFLINE";
        req.gameTokenData.token = config.operator.offlineTokens.settlement;
        return this.commitWinPayment(req);
    }

    @measure({ name: "PaymentService.finalizeGame", isAsync: true })
    public async finalizeGame({ request, ...req }: BrokenGameRequest<IntegrationGameTokenData>): Promise<Balance> {
        const newRequest: CommitPaymentRequest<IntegrationGameTokenData>["request"] = {
            deviceId: request.deviceId,
            roundId: request.roundId,
            eventId: request.eventId,
            roundEnded: true,
            transactionId: request.transactionId,
            win:
                request.finalizationType === BrandFinalizationType.FORCE_FINISH
                    ? 0
                    : request.roundStatistics.totalWin || 0,
            gameToken: undefined,
            ts: undefined
        };
        const token = req.gameTokenData.token;
        req.gameTokenData.authenticationType = "OFFLINE";
        req.gameTokenData.token = config.operator.offlineTokens.settlement;
        return await this.gamePlay({ request: newRequest, ...req }, new CommitWinHandler(), token);
    }

    private async getBalance(req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<Balance> {
        // Prevent getBalance request during finalization
        if (Boolean(req.request.finalizationType)) {
            return UNKNOWN_BALANCE;
        }
        try {
            return await this.retrieveBalance(req);
        } catch (err) {
            return UNKNOWN_BALANCE;
        }
    }

    private retrieveBalance(req: BalanceRequest<IntegrationGameTokenData>): Promise<Balance> {
        return this.soapGateway.request<
            BalanceRequest<IntegrationGameTokenData>,
            OperatorRetrieveBalancePayload,
            OperatorGamePlayResponse,
            Balance
        >(req, "retrieveBalance", new GetBalanceHandler(), req.gameTokenData.token);
    }

    private gamePlay(
        req: CommitPaymentRequest<IntegrationGameTokenData>,
        handler: SoapHandler<
            CommitPaymentRequest<IntegrationGameTokenData>,
            OperatorGamePlayPayload,
            OperatorGamePlayResponse,
            Balance
        >,
        token: string
    ): Promise<Balance> {
        return this.soapGateway.request<
            CommitPaymentRequest<IntegrationGameTokenData>,
            OperatorGamePlayPayload,
            OperatorGamePlayResponse,
            Balance
        >(req, "gamePlay", handler, token);
    }

    private requireRefundBet(error: any) {
        if (error instanceof MerchantInternalRollbackAndRetryError) {
            throw new RequireRefundBetError(error.message);
        }
    }
}
