import { CommitWinHand<PERSON> } from "./commitWin.handler";
import { betRequest } from "../../common/utils/mock-data-examples";
import { IntegrationGameTokenData, OperatorGamePlayResponse } from "../models";
import { logging } from "@skywind-group/sw-utils";
import { CommitPaymentRequest } from "@skywind-group/sw-integration-core";

logging.setUpOutput({ type: "console", logLevel: "info" });

const playRequest: CommitPaymentRequest<IntegrationGameTokenData> = {
    ...betRequest,
    request: {
        ...betRequest.request,
        win: 8.06
    }
};

describe("CommitWinHandler", () => {
    let commitWinHandler: CommitWinHandler;

    beforeEach(async () => {
        commitWinHandler = new CommitWinHandler();
    });

    it("should build correct request", () => {
        const result = commitWinHandler.build(playRequest);

        delete result["gamePlayRequest"]["baseRequest"]["requestId"];
        delete result["gamePlayRequest"]["baseRequest"]["timestamp"];

        expect(result).toEqual({
            gamePlayRequest: {
                amount: 8060,
                baseRequest: {
                    rgsAuthentication: {
                        rgsPassword: "***",
                        rgsUser: "SkyWind"
                    },
                    userAuthentication: {
                        accountId: "*********",
                        authenticationType: "ONLINE",
                        token: "***"
                    }
                },
                channel: "WEB",
                gameCode: "sw_live_erol_atom",
                gamePlayDescription: "Normal win",
                gamePlayId: *************,
                gameRoundCompleted: false,
                gameRoundId: "*************",
                playType: "SETTLEMENT",
                productName: "gaming",
                transactionId: "PtuDUxB85V4AAAMYPtuDVMBmDz4=_win"
            }
        });
    });

    it("should parse response correctly", () => {
        const response: OperatorGamePlayResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            transferId: "",
            balance: {
                mainAmount: 333876
            }
        };
        const result = commitWinHandler.parse(response, playRequest);
        expect(result).toEqual({
            main: 333.88,
            previousValue: 325.82,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 333.88
                }
            }
        });
    });

    it("should parse bonusAmount", () => {
        const response: OperatorGamePlayResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            transferId: "",
            balance: {
                mainAmount: 333876,
                bonusAmount: 9876
            }
        };
        const result = commitWinHandler.parse(response, playRequest);
        expect(result).toEqual({
            main: 343.75,
            previousValue: 335.69,
            extraData: {
                balance: {
                    bonusAmount: 9.88,
                    realAmount: 333.88
                }
            }
        });
    });

    it("should parse empty mainAmount", () => {
        const response: OperatorGamePlayResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            transferId: "",
            balance: {
                mainAmount: 0,
                bonusAmount: 9876
            }
        };
        const result = commitWinHandler.parse(response, playRequest);
        expect(result).toEqual({
            main: 9.88,
            previousValue: 1.82,
            extraData: {
                balance: {
                    bonusAmount: 9.88,
                    realAmount: 0
                }
            }
        });
    });
});
