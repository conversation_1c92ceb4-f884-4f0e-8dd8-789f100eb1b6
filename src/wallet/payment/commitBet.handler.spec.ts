import { CommitBetHandler } from "./commitBet.handler";
import { betRequest } from "../../common/utils/mock-data-examples";
import { OperatorGamePlayResponse } from "../models";
import { logging } from "@skywind-group/sw-utils";

logging.setUpOutput({ type: "console", logLevel: "info" });

describe("CommitBetHandler", () => {
    let commitBetHandler: CommitBetHandler;

    beforeEach(async () => {
        commitBetHandler = new CommitBetHandler();
    });

    it("should build correct request", () => {
        const expectedBalancePayload = {
            gamePlayRequest: {
                amount: 150120,
                baseRequest: {
                    rgsAuthentication: {
                        rgsPassword: "***",
                        rgsUser: "SkyWind"
                    },
                    userAuthentication: {
                        accountId: "*********",
                        authenticationType: "ONLINE",
                        token: "***"
                    }
                },
                channel: "WEB",
                gameCode: "sw_live_erol_atom",
                gamePlayDescription: "Normal Bet",
                gamePlayId: *************,
                gameRoundCompleted: false,
                gameRoundId: "*************",
                playType: "BET",
                productName: "gaming",
                transactionId: "PtuDUxB85V4AAAMYPtuDVMBmDz4=_bet"
            }
        };
        const result = commitBetHandler.build({
            ...betRequest,
            request: {
                ...betRequest.request,
                bet: 150.12
            }
        });

        delete result["gamePlayRequest"]["baseRequest"]["requestId"];
        delete result["gamePlayRequest"]["baseRequest"]["timestamp"];

        expect(result).toEqual(expectedBalancePayload);
    });

    it("should parse response correctly", () => {
        const response: OperatorGamePlayResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            transferId: "",
            balance: {
                mainAmount: 321457
            }
        };
        const result = commitBetHandler.parse(response, betRequest);
        expect(result).toEqual({
            main: 321.46,
            previousValue: 471.46,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 321.46
                }
            }
        });
    });

    it("should parse bonusAmount", () => {
        const response: OperatorGamePlayResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            transferId: "",
            balance: {
                mainAmount: 321457,
                bonusAmount: 9876
            }
        };
        const result = commitBetHandler.parse(response, betRequest);
        expect(result).toEqual({
            main: 331.33,
            previousValue: 481.33,
            extraData: {
                balance: {
                    bonusAmount: 9.88,
                    realAmount: 321.46
                }
            }
        });
    });

    it("should parse empty mainAmount", () => {
        const response: OperatorGamePlayResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            transferId: "",
            balance: {
                mainAmount: 0,
                bonusAmount: 9876
            }
        };
        const result = commitBetHandler.parse(response, betRequest);
        expect(result).toEqual({
            main: 9.88,
            previousValue: 159.88,
            extraData: {
                balance: {
                    bonusAmount: 9.88,
                    realAmount: 0
                }
            }
        });
    });
});
