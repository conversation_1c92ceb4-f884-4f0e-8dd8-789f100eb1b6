import { CommitPaymentRequest } from "@skywind-group/sw-integration-core";
import {
    createBaseRequest,
    getCorrespondingPlayId,
    IntegrationGameTokenData,
    OPERATOR_REQUEST_COMMAND,
    OperatorGamePlayPayload,
    OperatorGamePlayResponse
} from "../models";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { convertToBetfairAmount, fromBetfairBalance } from "../../common/utils/calculation";
import { SoapHandler } from "../../common/services/soap.service";

export class CommitBetHandler
    implements
        SoapHandler<
            CommitPaymentRequest<IntegrationGameTokenData>,
            OperatorGamePlayPayload,
            OperatorGamePlayResponse,
            Balance
        > {
    public readonly description = OPERATOR_REQUEST_COMMAND.BET;

    public build({ gameTokenData, merchantInfo, request }: CommitPaymentRequest<IntegrationGameTokenData>) {
        return {
            gamePlayRequest: {
                ...createBaseRequest(gameTokenData, merchantInfo),
                channel: request.deviceId.toUpperCase(),
                gameRoundId: request.roundId,
                gamePlayId: getCorrespondingPlayId(request.roundId, request.eventId, OPERATOR_REQUEST_COMMAND.BET),
                gamePlayDescription: "Normal Bet",
                gameRoundCompleted: request.roundEnded,
                transactionId: request.transactionId.publicId + "_bet",
                playType: OPERATOR_REQUEST_COMMAND.BET,
                amount: convertToBetfairAmount(request.bet)
            }
        };
    }

    public parse(res: OperatorGamePlayResponse, req: CommitPaymentRequest<IntegrationGameTokenData>) {
        return fromBetfairBalance(req.gameTokenData.currency, res.balance, req.request.bet);
    }
}
