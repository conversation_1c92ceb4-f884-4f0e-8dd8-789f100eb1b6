import { BalanceRequest, CommitPaymentRequest } from "@skywind-group/sw-integration-core";
import {
    createBaseRequest,
    IntegrationGameTokenData,
    OperatorBalanceResponse,
    OperatorRetrieveBalancePayload
} from "../models";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { fromBetfairBalance } from "../../common/utils/calculation";
import { SoapHandler } from "../../common/services/soap.service";

export class GetBalanceHandler
    implements
        SoapHandler<
            BalanceRequest<IntegrationGameTokenData>,
            OperatorRetrieveBalancePayload,
            OperatorBalanceResponse,
            Balance
        > {
    public readonly description = "";

    public build({ gameTokenData, merchantInfo }: CommitPaymentRequest<IntegrationGameTokenData>) {
        return {
            balanceRequest: createBaseRequest(gameTokenData, merchantInfo)
        };
    }

    public parse(res: OperatorBalanceResponse, req: CommitPaymentRequest<IntegrationGameTokenData>): Balance {
        return fromBetfairBalance(req.gameTokenData.currency, res.balance);
    }
}
