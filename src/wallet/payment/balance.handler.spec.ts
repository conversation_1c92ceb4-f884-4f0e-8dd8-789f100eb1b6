import { GetBalanceHandler } from "./balance.handler";
import { balanceRequest } from "../../common/utils/mock-data-examples";
import { OperatorBalanceResponse } from "../models";
import { logging } from "@skywind-group/sw-utils";

logging.setUpOutput({ type: "console", logLevel: "info" });

describe("GetBalanceHandler", () => {
    let getBalanceHandler: GetBalanceHandler;

    beforeEach(async () => {
        getBalanceHandler = new GetBalanceHandler();
    });

    it("should build correct request", () => {
        const expectedBalancePayload = {
            balanceRequest: {
                baseRequest: {
                    rgsAuthentication: {
                        rgsPassword: "***",
                        rgsUser: "SkyWind"
                    },
                    userAuthentication: {
                        accountId: "*********",
                        authenticationType: "ONLINE",
                        token: "***"
                    }
                },
                gameCode: "sw_live_erol_atom",
                productName: "gaming"
            }
        };

        const result = getBalanceHandler.build(balanceRequest);
        delete result["balanceRequest"]["baseRequest"]["requestId"];
        delete result["balanceRequest"]["baseRequest"]["timestamp"];
        expect(result).toEqual(expectedBalancePayload);
    });

    it("should parse response correctly", () => {
        const response: OperatorBalanceResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            balance: {
                mainAmount: 123987
            }
        };
        const result = getBalanceHandler.parse(response, balanceRequest);
        expect(result).toEqual({
            main: 123.99,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 123.99
                }
            }
        });
    });

    it("should parse bonusAmount", () => {
        const response: OperatorBalanceResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            balance: {
                mainAmount: 123987,
                bonusAmount: 9876
            }
        };
        const result = getBalanceHandler.parse(response, balanceRequest);
        expect(result).toEqual({
            main: 133.86,
            extraData: {
                balance: {
                    bonusAmount: 9.88,
                    realAmount: 123.99
                }
            }
        });
    });

    it("should parse empty mainAmount", () => {
        const response: OperatorBalanceResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            balance: {
                mainAmount: 0,
                bonusAmount: 9876
            }
        };
        const result = getBalanceHandler.parse(response, balanceRequest);
        expect(result).toEqual({
            main: 9.88,
            extraData: {
                balance: {
                    bonusAmount: 9.88,
                    realAmount: 0
                }
            }
        });
    });
});
