import { Test, TestingModule } from "@nestjs/testing";
import { PaymentService } from "./payment.service";
import { SoapGateway } from "../../common/services/soap.service";
import { balanceRequest, betRequest, finalizeRequest } from "../../common/utils/mock-data-examples";
import config from "../../config";
import { Names } from "../../names";
import { SoapModule } from "nestjs-soap";
import * as path from "path";
import * as nock from "nock";
import { logging } from "@skywind-group/sw-utils";
import { InterruptSocket } from "@skywind-group/sw-wallet-adapter-core";

logging.setUpOutput({ type: "console", logLevel: "info" });

function replyWith(filename: string, statusCode: nock.StatusCode = 200) {
    nock("https://bgi.betfair.com.nxt.ppbdev.com")
        .post("/BetfairGamingInterfaceService/v1.0")
        .replyWithFile(statusCode, path.join(__dirname, filename));
}

describe("PaymentService", () => {
    let mockPaymentService: jest.Mocked<PaymentService>;

    beforeEach(async () => {
        const moduleRef: TestingModule = await Test.createTestingModule({
            providers: [PaymentService, SoapGateway],
            imports: [
                SoapModule.registerAsync({
                    clientName: Names.SoapClient,
                    useFactory: async () => {
                        return {
                            uri: path.join(__dirname, "../../../wsdl.xml"),
                            clientOptions: {
                                envelopeKey: config.soap.envelopeKey,
                                overrideRootElement: {
                                    namespace: config.soap.namespace,
                                    xmlnsAttributes: [
                                        {
                                            name: "xmlns:" + config.soap.namespace,
                                            value: config.soap.xmlnsAttributesURL
                                        }
                                    ]
                                }
                            }
                        };
                    }
                })
            ]
        }).compile();

        mockPaymentService = moduleRef.get<PaymentService>(PaymentService) as jest.Mocked<PaymentService>;

        // Disable network connections by default.
        nock.disableNetConnect();
    });

    afterEach(() => {
        // Without this, nock causes a memory leak and the tests will fail on CI.
        // https://github.com/nock/nock/issues/1817
        nock.cleanAll();
    });

    it("should get balances", async () => {
        replyWith("./test/balance.xml");
        const result = await mockPaymentService.getBalances(balanceRequest);
        expect(result).toEqual({
            BRL: {
                main: 9999999832.8,
                extraData: {
                    balance: {
                        bonusAmount: 0,
                        realAmount: 9999999832.8
                    }
                }
            }
        });
    });

    it("should commit bet payment", async () => {
        replyWith("./test/game-play.xml");
        const result = await mockPaymentService.commitBetPayment(betRequest);
        expect(result).toEqual({
            main: 9999999836.8,
            previousValue: 9999999986.8,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 9999999836.8
                }
            }
        });
    });

    it("zero bet payment", async () => {
        replyWith("./test/balance.xml");
        const result = await mockPaymentService.commitBetPayment({
            ...betRequest,
            request: {
                ...betRequest.request,
                bet: 0
            }
        });
        expect(result).toEqual({
            main: 9999999832.8,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 9999999832.8
                }
            }
        });
    });

    it("should refund bet payment", async () => {
        replyWith("./test/game-play.xml");
        const result = await mockPaymentService.refundBetPayment(betRequest);
        expect(result).toEqual({
            main: 9999999836.8,
            previousValue: 9999999836.8,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 9999999836.8
                }
            }
        });
    });

    it("should commit win payment", async () => {
        replyWith("./test/game-play.xml");
        const result = await mockPaymentService.commitWinPayment({
            ...betRequest,
            request: {
                ...betRequest.request,
                win: 100
            }
        });
        expect(result).toEqual({
            main: 9999999836.8,
            previousValue: 9999999736.8,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 9999999836.8
                }
            }
        });
    });

    it("zero win payment", async () => {
        replyWith("./test/balance.xml");
        const result = await mockPaymentService.commitWinPayment({
            ...betRequest,
            request: {
                ...betRequest.request,
                win: 0
            }
        });
        expect(result).toEqual({
            main: 9999999832.8,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 9999999832.8
                }
            }
        });
    });

    it("should interrupt win payment", async () => {
        replyWith("./test/error-1000.xml", 500);
        let hasThrown = false;
        try {
            await mockPaymentService.commitWinPayment({
                ...betRequest,
                request: {
                    ...betRequest.request,
                    win: 100
                }
            });
        } catch (error) {
            hasThrown = true;
            expect(error).toBeInstanceOf(InterruptSocket);
        }
        expect(hasThrown).toBe(true);
    });

    it("should finalize with offline payments", async () => {
        replyWith("./test/game-play.xml");
        const result = await mockPaymentService.finalizeWithOfflinePayments({
            ...betRequest,
            request: {
                ...betRequest.request,
                win: 100
            }
        });
        expect(result).toEqual({
            main: 9999999836.8,
            previousValue: 9999999736.8,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 9999999836.8
                }
            }
        });
    });

    it("should finalize game", async () => {
        replyWith("./test/game-play.xml");
        const result = await mockPaymentService.finalizeGame(finalizeRequest);
        expect(result).toEqual({
            main: 9999999836.8,
            previousValue: 9999999836.8,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 9999999836.8
                }
            }
        });
    });
});
