import { CancelBet<PERSON>andler } from "./cancelBet.handler";
import { betRequest } from "../../common/utils/mock-data-examples";
import { OperatorGamePlayResponse } from "../models";
import { logging } from "@skywind-group/sw-utils";

logging.setUpOutput({ type: "console", logLevel: "info" });

describe("CancelBetHandler", () => {
    let cancelBetHandler: CancelBetHandler;

    beforeEach(async () => {
        cancelBetHandler = new CancelBetHandler();
    });

    it("should build correct request", () => {
        const result = cancelBetHandler.build({
            ...betRequest,
            request: {
                ...betRequest.request,
                bet: 150.12
            },
            gameTokenData: {
                ...betRequest.gameTokenData,
                authenticationType: "OFFLINE",
                token: "SkyWind_CANCEL"
            }
        });

        delete result["gamePlayRequest"]["baseRequest"]["requestId"];
        delete result["gamePlayRequest"]["baseRequest"]["timestamp"];

        expect(result).toEqual({
            gamePlayRequest: {
                baseRequest: {
                    rgsAuthentication: {
                        rgsPassword: "***",
                        rgsUser: "SkyWind"
                    },
                    userAuthentication: {
                        accountId: "*********",
                        authenticationType: "OFFLINE",
                        token: "SkyWind_CANCEL"
                    }
                },
                amount: 150120,
                gameCode: "sw_live_erol_atom",
                gamePlayDescription: "Cancel Bet",
                gamePlayId: *************,
                gameRoundCompleted: false,
                gameRoundId: "*************",
                playType: "CANCEL",
                productName: "gaming",
                transactionId: "PtuDUxB85V4AAAMYPtuDVMBmDz4=_bet"
            }
        });
    });

    it("should parse response correctly", () => {
        const response: OperatorGamePlayResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            transferId: "",
            balance: {
                mainAmount: 332123
            }
        };
        const result = cancelBetHandler.parse(response, betRequest);
        expect(result).toEqual({
            main: 332.12,
            previousValue: 332.12,
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 332.12
                }
            }
        });
    });

    it("should parse bonusAmount", () => {
        const response: OperatorGamePlayResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            transferId: "",
            balance: {
                mainAmount: 332123,
                bonusAmount: 9876
            }
        };
        const result = cancelBetHandler.parse(response, betRequest);
        expect(result).toEqual({
            main: 342,
            previousValue: 342,
            extraData: {
                balance: {
                    bonusAmount: 9.88,
                    realAmount: 332.12
                }
            }
        });
    });

    it("should parse empty mainAmount", () => {
        const response: OperatorGamePlayResponse = {
            baseResponse: {
                responseId: "",
                timestamp: ""
            },
            transferId: "",
            balance: {
                mainAmount: 0,
                bonusAmount: 9876
            }
        };
        const result = cancelBetHandler.parse(response, betRequest);
        expect(result).toEqual({
            main: 9.88,
            previousValue: 9.88,
            extraData: {
                balance: {
                    bonusAmount: 9.88,
                    realAmount: 0
                }
            }
        });
    });
});
