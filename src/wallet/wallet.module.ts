import { Module } from "@nestjs/common";
import { PaymentModule, StartGameModule } from "@skywind-group/sw-integration-core";
import config from "../config";
import { CreateGameTokenService } from "./start/createToken.service";
import { BaseHttpService, InternalAPIService } from "@skywind-group/sw-wallet-adapter-core";
import { Names } from "../names";
import { SoapModule } from "nestjs-soap";
import { SoapGateway } from "../common/services/soap.service";
import * as https from "https";
import axios from "axios";
import { PaymentService } from "./payment/payment.service";

// TODO: Check how this can be injected
const httpsAgent = new https.Agent({
    cert: config.soap.secure.cert,
    key: config.soap.secure.key,
    passphrase: config.soap.secure.passphrase,
    rejectUnauthorized: false,
    minVersion: "TLSv1.2"
});

const headers = {};
if (config.soap.additionalHeaders) {
    for (const header of config.soap.additionalHeaders) {
        headers[Object.keys(header).shift()] = Object.values(header).shift();
    }
}

const axiosInstance = axios.create({ httpsAgent, headers });

@Module({
    providers: [
        PaymentService,
        CreateGameTokenService,
        SoapGateway,
        { useValue: new BaseHttpService(config.operatorAPIBaseUrl), provide: Names.BaseHttpService },
        { useValue: new InternalAPIService(config.internalMAPIUrl), provide: Names.InternalAPIService }
    ],
    exports: [PaymentService, CreateGameTokenService, BaseHttpService, InternalAPIService, SoapGateway],
    imports: [
        StartGameModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                createGameToken: CreateGameTokenService,
                createGameURL: CreateGameTokenService
            },
            [WalletModule]
        ),
        PaymentModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                payment: PaymentService,
                refund: PaymentService
            },
            [WalletModule]
        ),
        SoapModule.registerAsync({
            clientName: Names.SoapClient,
            useFactory: async () => {
                return {
                    uri: config.soap.wsdlUrl,
                    clientOptions: {
                        request: axiosInstance,
                        envelopeKey: config.soap.envelopeKey,
                        overrideRootElement: {
                            namespace: config.soap.namespace,
                            xmlnsAttributes: [
                                {
                                    name: "xmlns:" + config.soap.namespace,
                                    value: config.soap.xmlnsAttributesURL
                                }
                            ]
                        }
                    }
                };
            },
            imports: [] // Inject the ConfigService
        })
    ]
})
export class WalletModule {}
