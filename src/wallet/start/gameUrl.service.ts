import { IntegrationInitRequest, IntegrationMerchantGameURLInfo } from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { CreateGameUrlRequest, CreateGameUrlSupport } from "@skywind-group/sw-integration-core";
import { measures } from "@skywind-group/sw-utils";

@Injectable()
export class GameUrlService implements CreateGameUrlSupport<IntegrationInitRequest> {

    @measures.measure({ name: "GameUrlService.createGameUrl", isAsync: true })
    public async createGameUrl(req: CreateGameUrlRequest<IntegrationInitRequest>): Promise<IntegrationMerchantGameURLInfo> {
        const tokenData: IntegrationMerchantGameURLInfo["tokenData"] = {
            merchantType: req.merchantInfo.type,
            merchantCode: req.merchantInfo.code,
            brandId: req.merchantInfo.brandId,
            providerCode: req.providerCode,

            gameCode: req.gameCode,
            providerGameCode: req.providerGameCode,

            playerCode: req.initRequest.customer,
            currency: req.initRequest.currency,
            country: req.initRequest.country,
            language: req.initRequest.language,

            token: req.initRequest.ticket,
        };
        return {
            urlParams: {},
            tokenData
        };
    }
}
