import {
    CreateGameTokenRequest,
    CreateGameTokenSupport,
    CreateGameUrlRequest
} from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationStartGameTokenData } from "../models";
import { MerchantGameTokenInfo, MerchantGameURLInfo } from "@skywind-group/sw-wallet-adapter-core";
import { Injectable } from "@nestjs/common";
import { CreateGameUrlSupport } from "@skywind-group/sw-integration-core/lib/interfaces/startGame.service";

@Injectable()
export class CreateGameTokenService
    implements
        CreateGameTokenSupport<IntegrationStartGameTokenData, IntegrationGameTokenData>,
        CreateGameUrlSupport<IntegrationGameTokenData> {
    public async createGameTokenData(
        req: CreateGameTokenRequest<IntegrationStartGameTokenData>
    ): Promise<MerchantGameTokenInfo<IntegrationGameTokenData>> {
        const gameTokenData = {
            gameCode: req.startGameToken.gameCode,
            currency: req.startGameToken.currency,
            merchantType: req.merchantInfo.type,
            merchantCode: req.merchantInfo.code,
            playmode: req.startGameToken.playmode,
            isPromoInternal: false,
            // specific
            brandId: req.merchantInfo.brandId,
            playerCode: req.startGameToken.playerCode,
            productName: req.startGameToken.productName,
            token: req.startGameToken.token
        };
        return { gameTokenData };
    }

    public async createGameUrl(req: CreateGameUrlRequest<IntegrationGameTokenData>): Promise<MerchantGameURLInfo> {
        const tokenData = {
            brandId: req.merchantInfo.brandId,
            merchantType: req.merchantInfo.type,
            merchantCode: req.merchantInfo.code,
            playerCode: req.initRequest.playerCode,
            gameCode: req.gameCode,
            providerGameCode: req.providerGameCode,
            providerCode: req.providerCode,
            currency: req.initRequest.currency,
            country: req.initRequest.country,
            playmode: req.initRequest.playmode,
            token: req.initRequest.token,
            productName: req.initRequest.productName
        };
        return {
            tokenData,
            urlParams: undefined
        };
    }
}
