// this should be the first line
import { measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import { bootstrapServer } from "@skywind-group/sw-integration-core";
import { LauncherModule } from "./launcher/launcher.module";
import config from "./config";

bootstrapServer({
    serviceName: "sw-integration-betfair-launcher",
    versionFile: "./lib/version",
    module: LauncherModule,
    internalPort: config.internalServer.launcher,
    port: config.server.launcherPort,
    secureKeys: config.soap.logSecureKeys
});
