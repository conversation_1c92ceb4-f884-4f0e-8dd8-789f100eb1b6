import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { OperationController } from "./operation.controller";
import { BetfairMockService } from "./mock.service";
import { WsdlController } from "./wsdl.controller";
import { OperationResponder } from "./operation.responder";

@Module({
    controllers: [OperationController, WsdlController],
    providers: [BetfairMockService],
    imports: [mock.MockModule]
})
export class MockModule implements NestModule {
    public configure(consumer: MiddlewareConsumer): any {
        consumer.apply(OperationResponder).forRoutes("BetfairGamingInterfaceService/v1.0");
    }
}
