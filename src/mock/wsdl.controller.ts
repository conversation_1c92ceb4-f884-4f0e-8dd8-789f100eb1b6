import { Controller, Get, Header, HttpException } from "@nestjs/common";
import { wsdl } from "./wsdl";

@Controller("wsdl")
export class WsdlController {
    @Get("BetfairGamingInterface_v1.0.wsdl")
    @Header("Content-Type", "text/xml; charset=utf-8")
    async wsdl() {
        try {
            return wsdl;
        } catch (e) {
            throw new HttpException(e.message, e.status);
        }
    }
}
