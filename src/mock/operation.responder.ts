import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { OperatorGamePlayPayload, OperatorRetrieveBalancePayload } from "../wallet/models";
import { convertToBetfairAmount } from "../common/utils/calculation";
import { v4 as uuidv4 } from "uuid";
import { XmlService } from "@skywind-group/sw-wallet-adapter-core";
import { removeKeysNamespace } from "../common/utils/xml";

@Injectable()
export class OperationResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(settingsService: mock.SettingsService) {
        super(
            settingsService,
            new XmlService(undefined, {
                attributeNamePrefix: "@_",
                attrNodeName: false, // default is false
                textNodeName: "#text",
                ignoreAttributes: false,
                cdataTagName: "__cdata", // default is false
                cdataPositionChar: "\\c",
                format: false,
                indentBy: "    ",
                supressEmptyNode: false
            })
        );
    }

    public async getResponse(_: any, payload: any) {
        const body = removeKeysNamespace(payload["soapenv:Envelope"]["soapenv:Body"]);
        const balance: OperatorRetrieveBalancePayload = body["RetrieveBalanceRequest"];
        const gamePlay: OperatorGamePlayPayload = body["GamePlayRequest"];
        if (balance) {
            return {
                "soapenv:Envelope": {
                    "@_xmlns:soapenv": "http://schemas.xmlsoap.org/soap/envelope/",
                    "soapenv:Body": {
                        RetrieveBalanceResponse: {
                            "@_xmlns": "http://www.betfair.com/servicetypes/v1/BetfairGamingInterface/",
                            response: {
                                baseResponse: {
                                    responseId: balance["balanceRequest"]?.["baseRequest"]?.["requestId"],
                                    timestamp: Date.now().toString()
                                },
                                balance: {
                                    mainAmount: convertToBetfairAmount(+this.settingsService.settings.amount)
                                }
                            }
                        }
                    }
                }
            };
        }
        if (gamePlay) {
            return {
                "soapenv:Envelope": {
                    "@_xmlns:soapenv": "http://schemas.xmlsoap.org/soap/envelope/",
                    "soapenv:Body": {
                        GamePlayResponse: {
                            "@_xmlns": "http://www.betfair.com/servicetypes/v1/BetfairGamingInterface/",
                            response: {
                                baseResponse: {
                                    responseId: gamePlay["gamePlayRequest"]?.["baseRequest"]?.["requestId"],
                                    timestamp: Date.now().toString()
                                },
                                transferId: uuidv4(),
                                balance: {
                                    mainAmount: convertToBetfairAmount(+this.settingsService.settings.amount)
                                }
                            }
                        }
                    }
                }
            };
        }
    }
}
