import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import {
    OperatorBalanceResponse,
    OperatorGamePlayPayload,
    OperatorGamePlayResponse,
    OperatorRetrieveBalancePayload
} from "../wallet/models";
import { v4 as uuidv4 } from "uuid";
import { convertFromBetfairAmount, toBetfairBalance } from "../common/utils/calculation";
import { Currencies } from "@skywind-group/sw-currency-exchange";

interface PaymentResponse {
    GamePlayResponse: {
        response: OperatorGamePlayResponse;
    };
}

@Injectable()
export class BetfairMockService extends mock.MockService {
    public constructor(
        customerService: mock.CustomerService,
        sessionService: mock.SessionService,
        ticketService: mock.TicketService,
        transactionService: mock.TransactionService
    ) {
        super(customerService, sessionService, ticketService, transactionService);
    }

    public getBalance(
        _merchant: mock.Merchant,
        customer: mock.Customer,
        {
            balanceRequest: {
                baseRequest: { requestId }
            }
        }: OperatorRetrieveBalancePayload
    ): { RetrieveBalanceResponse: { response: OperatorBalanceResponse } } {
        return {
            RetrieveBalanceResponse: {
                response: {
                    baseResponse: {
                        responseId: requestId,
                        timestamp: Date.now().toString()
                    },
                    balance: toBetfairBalance(customer.balance)
                }
            }
        };
    }

    protected getTicketId(): string {
        return "";
    }

    protected getAuthResponse(): any {
        return {};
    }

    debit(merchant: mock.Merchant, customer: mock.Customer, body: OperatorGamePlayPayload): PaymentResponse {
        body.gamePlayRequest.amount = convertFromBetfairAmount(body.gamePlayRequest.amount, this.currency(customer));
        return super.debit(merchant, customer, body);
    }

    credit(merchant: mock.Merchant, customer: mock.Customer, body: OperatorGamePlayPayload): PaymentResponse {
        body.gamePlayRequest.amount = convertFromBetfairAmount(body.gamePlayRequest.amount, this.currency(customer));
        return super.credit(merchant, customer, body);
    }

    protected getAmount({ gamePlayRequest: { amount } }: OperatorGamePlayPayload): number {
        return amount;
    }

    protected getTransactionId({ gamePlayRequest: { transactionId } }: OperatorGamePlayPayload): string {
        return transactionId;
    }

    protected getDebitTransactionIdInRollbackRequest(body: OperatorGamePlayPayload): string {
        return this.getTransactionId(body);
    }

    protected isFreebet(): boolean {
        return false;
    }

    protected getPaymentResponse(
        _merchant: mock.Merchant,
        customer: mock.Customer,
        _transaction: mock.Transaction,
        {
            gamePlayRequest: {
                baseRequest: { requestId }
            }
        }: OperatorGamePlayPayload
    ): PaymentResponse {
        return {
            GamePlayResponse: {
                response: {
                    baseResponse: {
                        responseId: requestId,
                        timestamp: Date.now().toString()
                    },
                    transferId: uuidv4(),
                    balance: toBetfairBalance(customer.balance)
                }
            }
        };
    }

    private currency(customer: mock.Customer) {
        const currencyCode = customer.currency_code || customer.balance?.currency_code;
        return currencyCode && Currencies.value(currencyCode);
    }
}
