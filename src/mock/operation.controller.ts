import {
    Body,
    Controller,
    Headers,
    HttpException,
    HttpStatus,
    Post,
    UseFilters,
    UseInterceptors
} from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { BetfairMockService } from "./mock.service";
import { OPERATOR_REQUEST_COMMAND, OperatorGamePlayPayload, OperatorRetrieveBalancePayload } from "../wallet/models";
import { SoapTransformerInterceptor } from "./soap-transformer.interceptor";
import { SoapErrorFilter } from "./soap-error.filter";
import { BgiException } from "../common/errors/betfair.errors";

@Controller("BetfairGamingInterfaceService")
@UseFilters(SoapErrorFilter)
@UseInterceptors(SoapTransformerInterceptor)
export class OperationController {
    constructor(
        private readonly service: BetfairMockService,
        private readonly ticketService: mock.TicketService,
        private readonly merchantService: mock.MerchantService,
        private readonly errorService: mock.CustomErrorService
    ) {}

    @Post("v1.0")
    public operation(@Body() body: any, @Headers("x-sw-player-token") token: string) {
        const balance: OperatorRetrieveBalancePayload = body["RetrieveBalanceRequest"];
        const gamePlay: OperatorGamePlayPayload = body["GamePlayRequest"];
        if (balance) {
            const { merchant, customer } = this.auth(token);
            this.throwActionError(merchant, customer, "balance", mock.RaiseType.BEFORE);
            try {
                return this.service.getBalance(merchant, customer, balance);
            } finally {
                this.throwActionError(merchant, customer, "balance", mock.RaiseType.AFTER);
            }
        }
        if (gamePlay) {
            const { merchant, customer } = this.auth(token);
            if (gamePlay.gamePlayRequest.playType === OPERATOR_REQUEST_COMMAND.BET) {
                this.throwActionError(merchant, customer, "bet", mock.RaiseType.BEFORE);
                try {
                    return this.service.debit(merchant, customer, gamePlay);
                } finally {
                    this.throwActionError(merchant, customer, "bet", mock.RaiseType.AFTER);
                }
            }
            if (gamePlay.gamePlayRequest.playType === OPERATOR_REQUEST_COMMAND.WIN) {
                this.throwActionError(merchant, customer, "settlement", mock.RaiseType.BEFORE);
                try {
                    return this.service.credit(merchant, customer, gamePlay);
                } finally {
                    this.throwActionError(merchant, customer, "settlement", mock.RaiseType.AFTER);
                }
            }
            if (gamePlay.gamePlayRequest.playType === OPERATOR_REQUEST_COMMAND.CANCEL) {
                this.throwActionError(merchant, customer, "cancel", mock.RaiseType.BEFORE);
                try {
                    return this.service.rollback(merchant, customer, gamePlay);
                } finally {
                    this.throwActionError(merchant, customer, "cancel", mock.RaiseType.AFTER);
                }
            }
        }
        throw new HttpException("Not found", HttpStatus.NOT_FOUND);
    }

    private auth(token: string) {
        const ticket = this.ticketService.getById(token);
        if (!ticket) {
            throw new BgiException("INVALID_TOKEN_ERROR", 2000);
        }
        const { merchantId, customerId } = ticket;
        const merchant = this.merchantService.getById(merchantId);
        if (!merchant) {
            throw new BgiException("ACCOUNT_ID_TOKEN_MISMATCH_ERROR", 2009);
        }
        const customer = this.merchantService.getCustomerById(merchantId, customerId);
        if (!customer) {
            throw new BgiException("INVALID_RGS_CREDENTIALS_ERROR", 2010);
        }
        return { merchant, customer };
    }

    private throwActionError(
        merchant: mock.Merchant,
        customer: mock.Customer,
        action: string,
        raiseType: mock.RaiseType
    ) {
        const actionErrors = this.errorService.getActionErrors(merchant.merch_id, customer.cust_id, action);
        const error = (actionErrors[raiseType] || []).shift();
        if (error) {
            throw new BgiException(error.error_msg, error.error_code);
        }
    }
}
