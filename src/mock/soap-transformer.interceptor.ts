import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from "@nestjs/common";
import { map } from "rxjs/operators";
import { XmlService } from "@skywind-group/sw-wallet-adapter-core";

@Injectable()
export class SoapTransformerInterceptor implements NestInterceptor {
    private readonly service = new XmlService();

    intercept(context: ExecutionContext, next: CallHandler) {
        const res = context.switchToHttp().getResponse();
        res.header("content-type", "text/xml; charset=utf-8");
        return next.handle().pipe(
            map((data) =>
                this.service.convertToXML({
                    "soapenv:Envelope": {
                        "soapenv:Body": data
                    }
                })
            )
        );
    }
}
