import { ArgumentsHost, Catch, ExceptionFilter, HttpException } from "@nestjs/common";
import { FastifyReply } from "fastify";
import { XmlService } from "@skywind-group/sw-wallet-adapter-core";
import { setKeysNamespace } from "../common/utils/xml";
import { BgiException } from "../common/errors/betfair.errors";

function toBgiException(error: Error): BgiException {
    if (error instanceof BgiException) {
        return error;
    }
    return new BgiException("INTERNAL_ERROR", 1000, error.message);
}

@Catch()
export class SoapErrorFilter implements ExceptionFilter {
    private readonly service = new XmlService();

    public catch(error: Error, host: ArgumentsHost) {
        const response = host.switchToHttp().getResponse<FastifyReply<any>>();
        if (error instanceof HttpException) {
            response.header("content-type", "text/plain; charset=utf-8");
            response.status(error.getStatus());
            response.send(error.message);
        } else {
            const err = toBgiException(error);
            response.header("content-type", "text/xml; charset=utf-8");
            response.status(500);
            response.send(
                this.service.convertToXML({
                    "soapenv:Envelope": {
                        "soapenv:Body": {
                            "soapenv:Fault": {
                                faultcode: "soapenv:Server",
                                faultstring: `BGI-${err.code}`,
                                detail: setKeysNamespace({
                                    BgiException: {
                                        errorCode: err.message,
                                        message: err.description
                                    }
                                })
                            }
                        }
                    }
                })
            );
        }
    }
}
