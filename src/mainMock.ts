// this should be the first line
import { measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import { bootstrapMock, BootstrapMockConfig } from "@skywind-group/sw-integration-core";
import { MockModule } from "./mock/mock.module";
import { NestFastifyApplication } from "@nestjs/platform-fastify";
import * as plugin from "./common/utils/mock-plugin";
import config from "./config";

async function start(config: BootstrapMockConfig): Promise<void> {
    const app = (await bootstrapMock(config)) as NestFastifyApplication;
    app.register(plugin);
}

start({
    serviceName: "sw-integration-betfair-mock",
    versionFile: "./lib/version",
    module: MockModule,
    internalPort: config.internalServer.mock,
    port: config.server.mockPort,
    secureKeys: config.soap.logSecureKeys,
    actions: ["balance", "bet", "settlement", "cancel"]
});
