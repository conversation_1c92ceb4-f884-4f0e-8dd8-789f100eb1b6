import {
    Body,
    Controller,
    Get,
    HttpException,
    Post,
    Query,
    Redirect,
    UseFilters,
    ValidationPipe
} from "@nestjs/common";
import { LauncherService } from "./launcher.service";
import { ErrorFilter } from "../../common/filters/error.filter";
import { GameLaunchUrlRequest } from "../../common/intefraces/betfair.interface";

@Controller("launch")
@UseFilters(ErrorFilter)
export class LauncherController {
    constructor(private readonly launcherService: LauncherService) {}

    @Get("/game")
    @Redirect()
    async getGameUrl(@Query(new ValidationPipe()) request: GameLaunchUrlRequest) {
        try {
            return await this.launcherService.getGameUrl(request);
        } catch (e) {
            throw new HttpException(e.message, e.status);
        }
    }

    @Post("/game")
    @Redirect()
    async getGameUrlPost(@Body(new ValidationPipe()) request: GameLaunchUrlRequest) {
        try {
            return await this.launcherService.getGameUrl(request);
        } catch (e) {
            throw new HttpException(e.message, e.status);
        }
    }
}
