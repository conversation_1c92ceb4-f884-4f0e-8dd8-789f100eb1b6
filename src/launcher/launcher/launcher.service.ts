import { Inject, Injectable } from "@nestjs/common";
import { Names } from "../../names";
import { BaseHttpService, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import config from "../../config";
import { BetfairJurisdiction, GameLaunchUrlRequest } from "../../common/intefraces/betfair.interface";
import {
    IntegrationMerchantGameInitRequest,
    LaunchObject,
    SkywindJurisdiction,
    SWGameLaunchResponse
} from "../../common/intefraces/skywind.interface";
import { measures } from "@skywind-group/sw-utils";
import measure = measures.measure;

@Injectable()
export class LauncherService {
    constructor(@Inject(Names.BaseHttpService) private readonly baseHttpService: BaseHttpService) {}

    private static GAME_URL = "v1/merchants/game/url";

    @measure({ name: "LauncherService.getGameUrl", isAsync: true })
    public async getGameUrl(gameUrlRequest: GameLaunchUrlRequest): Promise<LaunchObject> {
        const { url } = await this.baseHttpService.post<SWGameLaunchResponse>(
            LauncherService.GAME_URL,
            this.mapToSW(gameUrlRequest)
        );
        return { url };
    }

    protected mapToSW({ language, ...request }: GameLaunchUrlRequest): IntegrationMerchantGameInitRequest {
        const playMode = request.realMode === "true" ? PlayMode.REAL : PlayMode.FUN;
        const jurisdiction = LauncherService.getJurisdiction(request.jurisdiction);
        return {
            merchantType: request.merchantType || config.merchantType,
            merchantCode: `${request.operator}__${jurisdiction}`,
            ticket: request.token,
            gameCode: request.gameCode,
            playMode: playMode,
            playmode: playMode,
            platform: request.platform,
            language: language === "pt" ? "pt-br" : language,
            currency: request.currency,
            playerCode: request.userId,
            token: request.token,
            productName: request.productName
        };
    }

    private static getJurisdiction(jurisdiction: string): string {
        switch (jurisdiction) {
            case BetfairJurisdiction.ITALY:
                return BetfairJurisdiction.ITALY;
            case BetfairJurisdiction.SPAIN:
                return SkywindJurisdiction.SPAIN;
            case BetfairJurisdiction.SWEDEN:
                return SkywindJurisdiction.SWEDEN;
            case BetfairJurisdiction.ROMANIA:
                return SkywindJurisdiction.ROMANIA;
            case BetfairJurisdiction.INTERNATIONAL:
            default:
                return SkywindJurisdiction.MT;
        }
    }
}
