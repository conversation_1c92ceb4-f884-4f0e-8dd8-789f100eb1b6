import { Test, TestingModule } from "@nestjs/testing";
import { LauncherService } from "./launcher.service";
import { expect } from "chai";
import { BaseHttpService } from "@skywind-group/sw-wallet-adapter-core";
import config from "../../config";
import { Names } from "../../names";

describe("LauncherService", () => {
    let service: LauncherService;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                LauncherService,
                { useValue: new BaseHttpService(config.operatorAPIBaseUrl), provide: Names.BaseHttpService }
            ]
        }).compile();

        service = module.get<LauncherService>(LauncherService);
    });

    it("should be defined", () => {
        expect(service).to.be.exist;
    });
});
