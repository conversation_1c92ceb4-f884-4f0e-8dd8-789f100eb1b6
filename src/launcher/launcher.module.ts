import { Modu<PERSON> } from "@nestjs/common";
import { LauncherController } from "./launcher/launcher.controller";
import { LauncherService } from "./launcher/launcher.service";
import { BaseHttpService } from "@skywind-group/sw-wallet-adapter-core";
import config from "../config";
import { Names } from "../names";

@Module({
    controllers: [LauncherController],
    providers: [
        LauncherService,
        { useValue: new BaseHttpService(config.operatorAPIBaseUrl), provide: Names.BaseHttpService }
    ]
})
export class LauncherModule {}
