// this should be the first line
import { measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import { bootstrapServer } from "@skywind-group/sw-integration-core";
import { WalletModule } from "./wallet/wallet.module";
import config from "./config";

bootstrapServer({
    serviceName: "sw-integration-betfair-wallet",
    versionFile: "./lib/version",
    module: WalletModule,
    internalPort: config.internalServer.wallet,
    port: config.server.walletPort,
    secureKeys: config.soap.logSecureKeys
});
