import { ERROR_LEVEL, SWError } from "@skywind-group/sw-wallet-adapter-core";
import { BetfairErrorCodes } from "./betfair.errors";

export class CustomSWError extends SWError {
    constructor(status: number, code: number, message: string) {
        super(status, code, message, ERROR_LEVEL.WARN);
    }
}

export class MerchantInternalError extends CustomSWError {
    constructor(message: string) {
        super(500, 506, message);
        this.data.reason = message;
    }
}

export class MerchantInternalRollbackAndRetryError extends CustomSWError {
    constructor(message: string) {
        super(500, 809, message);
        this.data.reason = message;
    }
}

export class SessionNotFoundError extends CustomSWError {
    constructor(message: string) {
        super(404, 105, message);
    }
}

export class PlayerIsSelfExcludedError extends CustomSWError {
    constructor(message: string) {
        super(403, 1501, message);
    }
}

export class CountryIsRestricted extends SWError {
    constructor(message: string) {
        super(403, 701, message);
    }
}

export class PlayerIsSuspended extends SWError {
    constructor(message: string) {
        super(400, 712, message);
    }
}

export class RGRealityCheckError extends CustomSWError {
    constructor(message: string) {
        super(403, 1505, message);
    }
}

export class RGSessionLossLimitReachedError extends CustomSWError {
    constructor(message: string) {
        super(403, 1513, message);
    }
}

export class RGRegulatoryCustomError extends CustomSWError {
    constructor(message: string) {
        super(403, 1514, message);
    }
}

export class ValidationError extends CustomSWError {
    constructor(message: string) {
        super(400, 40, message);
    }
}

export class BalanceError extends CustomSWError {
    constructor(message: string) {
        super(400, 200, message);
    }
}

export function mapToSwError(errorCode: string | undefined): SWError {
    switch (errorCode) {
        case BetfairErrorCodes.SELF_EXCLUDED_ERROR:
            return new PlayerIsSelfExcludedError(errorCode);
        case BetfairErrorCodes.BANNED_COUNTRY_ERROR:
            return new CountryIsRestricted(errorCode);
        case BetfairErrorCodes.INVALID_RGS_CREDENTIALS_ERROR:
            return new ValidationError(errorCode);
        case BetfairErrorCodes.REGULATORY_MESSAGE_ERROR:
            return new RGRealityCheckError(errorCode);
        case BetfairErrorCodes.PRODUCT_LOSS_LIMIT_ERROR:
            return new RGSessionLossLimitReachedError(errorCode);
        case BetfairErrorCodes.BALANCE_ERROR:
            return new BalanceError(errorCode);

        case BetfairErrorCodes.ACCOUNT_LOCK_ERROR:
        case BetfairErrorCodes.ACCOUNT_SUSPENDED:
            return new PlayerIsSuspended(errorCode);

        case BetfairErrorCodes.INVALID_TOKEN_ERROR:
        case BetfairErrorCodes.ACCOUNT_ID_TOKEN_MISMATCH_ERROR:
            return new SessionNotFoundError(errorCode);

        case BetfairErrorCodes.REALITY_CHECK_ACKNOWLEDGE_ERROR:
        case BetfairErrorCodes.REALITY_CHECK_ELAPSED_ERROR:
        case BetfairErrorCodes.GLOBAL_LOSS_LIMIT_ERROR:
            return new RGRegulatoryCustomError(errorCode);

        case BetfairErrorCodes.INVALID_INPUT_ERROR:
        case BetfairErrorCodes.KYC_SUSPENDED_ERROR:
        case BetfairErrorCodes.KYC_INCOMPLETE_ERROR:
        case BetfairErrorCodes.PENDING_AUTH_ERROR:
        case BetfairErrorCodes.MASTER_ACCOUNT_ERROR:
        case BetfairErrorCodes.UNAUTHORIZED_OPERATION:
        case BetfairErrorCodes.CANCEL_FAILED_ERROR:
        case BetfairErrorCodes.RETRIEVE_BALANCE_ERROR:
            return new MerchantInternalError(errorCode);

        case BetfairErrorCodes.INTERNAL_ERROR:
        case BetfairErrorCodes.TRANSFER_FAILED_ERROR:
        default:
            // Retry / Cancel error
            return new MerchantInternalRollbackAndRetryError(errorCode);
    }
}
