export enum BetfairErrorCodes {
    INTERNAL_ERROR = "INTERNAL_ERROR",
    INVALID_INPUT_ERROR = "INVALID_INPUT_ERROR",
    INVALID_TOKEN_ERROR = "INVALID_TOKEN_ERROR",
    SELF_EXCLUDED_ERROR = "SELF_EXCLUDED_ERROR",
    BANNED_COUNTRY_ERROR = "BANNED_COUNTRY_ERROR",
    ACCOUNT_LOCK_ERROR = "ACCOUNT_LOCK_ERROR",
    ACCOUNT_SUSPENDED = "ACCOUNT_SUSPENDED",
    KYC_SUSPENDED_ERROR = "KYC_SUSPENDED_ERROR",
    KYC_INCOMPLETE_ERROR = "KYC_INCOMPLETE_ERROR",
    PENDING_AUTH_ERROR = "PENDING_AUTH_ERROR",
    MASTER_ACCOUNT_ERROR = "MASTER_ACCOUNT_ERROR",
    ACCOUNT_ID_TOKEN_MISMATCH_ERROR = "ACCOUNT_ID_TOKEN_MISMATCH_ERROR",
    INVALID_RGS_CREDENTIALS_ERROR = "INVALID_RGS_CREDENTIALS_ERROR",
    UNAUTHORIZED_OPERATION = "UNAUTHORIZED_OPERATION",
    REALITY_CHECK_ACKNOWLEDGE_ERROR = "REALITY_CHECK_ACKNOWLEDGE_ERROR",
    REALITY_CHECK_ELAPSED_ERROR = "REALITY_CHECK_ELAPSED_ERROR",
    REGULATORY_MESSAGE_ERROR = "REGULATORY_MESSAGE_ERROR",
    TRANSFER_FAILED_ERROR = "TRANSFER_FAILED_ERROR",
    GLOBAL_LOSS_LIMIT_ERROR = "GLOBAL_LOSS_LIMIT_ERROR",
    PRODUCT_LOSS_LIMIT_ERROR = "PRODUCT_LOSS_LIMIT_ERROR",
    BALANCE_ERROR = "BALANCE_ERROR",
    CANCEL_FAILED_ERROR = "CANCEL_FAILED_ERROR",
    RETRIEVE_BALANCE_ERROR = "RETRIEVE_BALANCE_ERROR"
}

export class BgiException extends Error {
    constructor(message: string, public readonly code: string | number, public readonly description?: string) {
        super(message);
    }
}
