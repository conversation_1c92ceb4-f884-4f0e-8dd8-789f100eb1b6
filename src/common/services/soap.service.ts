import { Inject, Injectable } from "@nestjs/common";
import { logging } from "@skywind-group/sw-utils";
import { Client } from "nestjs-soap";
import { Names } from "../../names";
import { mapToSwError } from "../errors/sw.errors";
import { getSecuredObjectData, getSecuredXmlData } from "@skywind-group/sw-wallet-adapter-core";
import { setKeysNamespace } from "../utils/xml";
import config from "../../config";

const logger = logging.logger("soap");

function removeCircularReference(obj: any): any {
    try {
        let cache = [];
        const str = JSON.stringify(obj, function (key, value) {
            if (typeof value === "object" && value !== null) {
                if (cache.indexOf(value) !== -1) {
                    // Found circular reference, discard key
                    return;
                }
                // Store value in our collection
                cache.push(value);
            }
            return value;
        });
        cache = null; // reset the cache
        return JSON.parse(str);
    } catch (error) {
        logger.error(
            {
                response: `${obj}`,
                error: logging.errAsObject(error as any)
            },
            "Internal server error"
        );
    }
}

function getSecuredXml(data: any): any {
    try {
        return getSecuredXmlData(data, config.soap.logSecureKeys);
    } catch (error) {
        logger.error(error, "Internal server error");
    }
}

function getSecuredObject(data: any): any {
    try {
        return getSecuredObjectData(data, config.soap.logSecureKeys);
    } catch (error) {
        logger.error(error, "Internal server error");
    }
}

export type SoapRequestBuild<REQ, PREQ> = (req: REQ) => PREQ;
export type SoapResponseParser<PRES, RES, REQ> = (res: PRES, req: REQ) => RES;

export interface SoapHandler<REQ, PREQ, PRES, RES> {
    description: string;
    build: SoapRequestBuild<REQ, PREQ>;
    parse: SoapResponseParser<PRES, RES, REQ>;
}

@Injectable()
export class SoapGateway {
    private static readonly tsSymbol: any = Symbol.for("SoapGateway_ts");

    public constructor(@Inject(Names.SoapClient) private readonly agent: Client) {}

    public async request<REQ, PREQ, PRES, RES>(
        request: REQ,
        action: string,
        handler: SoapHandler<REQ, PREQ, PRES, RES>,
        token: string
    ): Promise<RES> {
        const payload = {
            ...setKeysNamespace(handler.build(request)),
            [SoapGateway.tsSymbol]: Date.now()
        };
        if (!this.agent) {
            throw new Error("SOAP client is not initialized");
        }
        if (config.soap.url) {
            this.agent.setEndpoint(config.soap.url); // this is uri from soap module options
        }
        // TODO How I want to manage this?
        this.agent.setSOAPAction(action); // this is method from request handlers
        if (config.isMockWallet) {
            this.agent.addHttpHeader("x-sw-player-token", token);
        }

        logger.info(
            {
                request: {
                    method: action,
                    url: config.soap.url,
                    data: getSecuredObject(payload)
                }
            },
            `POST ${action} ${handler.description}`
        );
        try {
            const [result, rawResponse, , rawRequest] = await this.agent[action + "Async"](payload);
            logger.info(
                {
                    rawRequest: getSecuredXml(rawRequest),
                    request: getSecuredObject(payload),
                    requestTime: (Date.now() - payload[SoapGateway.tsSymbol]) / 1000,
                    rawResponse: getSecuredXml(rawResponse),
                    response: removeCircularReference(result.response)
                },
                "merchant api request/response"
            );
            const parser = handler.parse.bind(handler);
            return parser(result.response, request);
        } catch (error) {
            logger.error(
                {
                    request: getSecuredObject(payload),
                    requestTime: (Date.now() - payload[SoapGateway.tsSymbol]) / 1000,
                    error: logging.errAsObject(error as any)
                },
                "merchant api request/error"
            );
            throw mapToSwError(error?.root?.Envelope?.Body?.Fault?.detail?.BgiException?.errorCode);
        }
    }
}
