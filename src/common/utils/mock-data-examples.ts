import { BrandFinalizationType, ITrxId, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { CommitPaymentRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData } from "../../wallet/models";

const gameTokenData = {
    gameCode: "sw_live_erol_atom",
    currency: "BRL",
    merchantType: "betfair",
    merchantCode: "betfair__MT",
    playmode: PlayMode.REAL,
    brandId: 3367,
    playerCode: "321279977",
    productName: "gaming",
    token: "***",
    deviceId: "web",
    envId: "gs-ro",
    test: false,
    freeBetsDisabled: true,
    isMultibet: false,
    isLiveGame: true,
    isPromoInternal: false
};

const merchantInfo = {
    type: "betfair",
    code: "betfair__MT",
    params: {
        password: "***",
        username: "Sky<PERSON>ind",
        keepAliveSec: 30,
        supportTransfer: true
    },
    isTest: false,
    lastTestsPassing: null,
    brandId: 3367,
    brandTitle: "Betfair MT merchant"
};

const request = {
    currency: "BRL",
    transactionId: {
        publicId: "PtuDUxB85V4AAAMYPtuDVMBmDz4=",
        serialId: 15309006406,
        ts: 1701861622612,
        timestamp: 1701861622612
    } as ITrxId,
    deviceId: "web",
    roundId: "3005776665734",
    roundPID: "G4Ge02b8G",
    gameSessionId: "3000066282917",
    roundEnded: false,
    actions: [
        {
            action: "debit",
            attribute: "balance",
            amount: 150,
            changeType: "bet"
        }
    ],
    operation: "bet",
    eventId: 0,
    totalEventId: 0,
    ts: "2023-12-06T11:20:22.616Z",
    gameToken:
        "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X2xpdmVfZXJvbF9hdG9tIiwiY3VycmVuY3kiOiJCUkwiLCJtZXJjaGFudFR5cGUiOiJiZXRmYWlyIiwibWVyY2hhbnRDb2RlIjoiYmV0ZmFpcl9fTVQiLCJwbGF5bW9kZSI6InJlYWwiLCJicmFuZElkIjozMzY3LCJwbGF5ZXJDb2RlIjoiMzIxMjc5OTc3IiwicHJvZHVjdE5hbWUiOiJnYW1pbmciLCJ0b2tlbiI6IlFkTFVwVmpnRTFIY25ITFlHSk5yQXkwcmVBNi1zUkVHV0hDSUpDa25PUExCT0trOGRvQlhPaWdXUmViM1JBWEczaGRCdmYyUk5iOEl0dzJLZWliRy15ek1qZnlmRHJoZ2tyVHNYZFJrTDZ3cXViMjdXTjNzMzNKYXNST19OaXlNIiwiZGV2aWNlSWQiOiJ3ZWIiLCJlbnZJZCI6ImdzLXJvIiwidGVzdCI6ZmFsc2UsImZyZWVCZXRzRGlzYWJsZWQiOnRydWUsImlzTXVsdGliZXQiOmZhbHNlLCJpc0xpdmVHYW1lIjp0cnVlLCJpYXQiOjE3MDE4NjE1NTgsImlzcyI6InNreXdpbmRncm91cCJ9.9dCEVuv7aV5o-yZ85c0WITajF7L_5WlnpKFtjOE-jNU5EqeT8Hgc_RCXjO-z6-bV08NuFuaU5RiaiyciW2BjOg",
    operatorSiteId: 10597,
    bet: 150,
    win: 0,
    isJPWin: false
};

const finalize = {
    roundStatistics: {
        totalBet: 150,
        totalWin: 0,
        totalEvents: 0,
        balanceBefore: 1000,
        balanceAfter: 850
    },
    finalizationType: BrandFinalizationType.OFFLINE_PAYMENTS
};

const baseRequest = {
    gameTokenData,
    merchantInfo
};

export const balanceRequest = baseRequest as CommitPaymentRequest<IntegrationGameTokenData>;

export const betRequest = {
    ...baseRequest,
    request
};

export const finalizeRequest = {
    ...baseRequest,
    request: {
        ...request,
        ...finalize
    }
};
