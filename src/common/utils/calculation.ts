import { calculation } from "@skywind-group/sw-utils";
import config from "../../config";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { Currencies, Currency } from "@skywind-group/sw-currency-exchange";
import { OperatorBalanceObject } from "../../wallet/models";
import { mock } from "@skywind-group/sw-integration-core";

const precision = Math.pow(10, config.precision);

export function toBetfairBalance(balance?: mock.Customer["balance"]): OperatorBalanceObject {
    const amount = convertToBetfairAmount(+(balance?.amount || ""));
    const bonusAmount = convertToBetfairAmount(+(balance?.extra_data?.bonus_amount || ""));
    return {
        mainAmount: amount,
        totalAmount: amount + bonusAmount,
        bonusAmount
    };
}

export function convertToBetfairAmount(amount: number): number {
    return +(normalizeAmount(amount) * precision).toFixed(0);
}

export function convertFromBetfairAmount(amount: number, currency?: Currency): number {
    const value = amount / precision;
    return currency ? currency.toFixedByExponent(value) : value;
}

export function fromBetfairBalance(
    currencyCode: string,
    { mainAmount: realAmount, bonusAmount = 0 }: OperatorBalanceObject,
    amount?: number
) {
    const currency = Currencies.get(currencyCode);
    const balance: Balance = {
        main: convertFromBetfairAmount(realAmount + bonusAmount, currency),
        extraData: {
            balance: {
                realAmount: convertFromBetfairAmount(realAmount, currency),
                bonusAmount: convertFromBetfairAmount(bonusAmount, currency)
            }
        }
    };
    if (amount !== undefined) {
        balance.previousValue = currency.toFixedByExponent(balance.main + normalizeAmount(amount));
    }
    return balance;
}

function normalizeAmount(amount?: number): number {
    return calculation.normalizeAmountByPrecision(config.precision, amount || 0);
}
