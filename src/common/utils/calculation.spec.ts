import { logging } from "@skywind-group/sw-utils";
import { convertFromBetfairAmount, convertToBetfairAmount, fromBetfairBalance } from "./calculation";
import { Currencies } from "@skywind-group/sw-currency-exchange";

logging.setUpOutput({ type: "console", logLevel: "info" });

describe("calculation", () => {
    it("convertToBetfairAmount", () => {
        expect(convertToBetfairAmount(514.29)).toEqual(514290);
    });

    it("convertFromBetfairAmount", () => {
        expect(convertFromBetfairAmount(514290, Currencies.get("EUR"))).toEqual(514.29);
    });

    it("fromBetfairBalance", () => {
        expect(
            fromBetfairBalance("BRL", {
                totalAmount: 589560,
                mainAmount: 589560,
                bonusAmount: 0
            })
        ).toEqual({
            extraData: {
                balance: {
                    bonusAmount: 0,
                    realAmount: 589.56
                }
            },
            main: 589.56
        });
    });
});
