import config from "../../config";

export function setKeysNamespace<T>(obj: T, namespace = config.soap.namespace): T {
    if (obj === null || typeof obj !== "object") {
        // Not an object, return it as is
        return obj;
    }

    return Object.keys(obj).reduce((newObj, key) => {
        const newKey = `${namespace}:${key}`.toString();
        const value = obj[key];

        // If the value is an object, recursively call setKeysNamespace
        newObj[newKey] = typeof value === "object" && value !== null ? setKeysNamespace(value) : value;

        return newObj;
    }, {}) as T;
}

export function removeKeysNamespace<T = any>(obj: T, namespace = config.soap.namespace): T {
    if (obj === null || typeof obj !== "object") {
        return obj;
    }
    namespace = namespace.endsWith(":") ? namespace : `${namespace}:`;
    return Object.keys(obj).reduce((newObj, key) => {
        const value = obj[key];
        const newKey = key.startsWith(namespace) ? key.slice(namespace.length) : key;
        newObj[newKey] = removeKeysNamespace(value, namespace);
        return newObj;
    }, {}) as T;
}
