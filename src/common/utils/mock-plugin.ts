import * as fp from "fastify-plugin";
import { XmlService } from "@skywind-group/sw-wallet-adapter-core";
import { X2jOptionsOptional } from "fast-xml-parser";
import * as fastify from "fastify";
import { IncomingMessage, Server, ServerResponse } from "http";
import { removeKeysNamespace } from "./xml";

const fastifyXml: fastify.Plugin<Server, IncomingMessage, ServerResponse, X2jOptionsOptional> = (
    fastify,
    opts,
    done
) => {
    const service = new XmlService(opts);
    fastify.addContentTypeParser(["text/xml", "application/xml"], { parseAs: "string" }, function (_req, body, done) {
        done(null, removeKeysNamespace(service.convertToObject(body)["soapenv:Envelope"]["soapenv:Body"]));
    });
    done();
};

module.exports = fp(fastifyXml, {
    fastify: ">=2.0.0",
    name: "fastify-xml"
});
