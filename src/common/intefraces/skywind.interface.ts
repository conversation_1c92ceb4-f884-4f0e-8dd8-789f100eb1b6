import { MerchantGameInitRequest } from "@skywind-group/sw-wallet-adapter-core";

export interface IntegrationMerchantGameInitRequest extends MerchantGameInitRequest {
    country?: string;
    playerCode?: string;
    currency?: string;
    platform?: string;
    token?: string;
    productName?: string;
    jurisdiction?: string;
}

export interface LaunchObject {
    url: string;
}

export interface SWGameLaunchResponse {
    url: string;
    token: string;
}

export enum SkywindJurisdiction {
    MT = "MT",
    ITALY = "IT",
    SWEDEN = "SW",
    SPAIN = "SP",
    ROMANIA = "RO"
}
