export interface SoapGatewayConfig {
    envelopeKey: string;
    namespace: string;
    xmlnsAttributesURL?: string;

    /**
     * Base URL for SOAP communication. Single argument required for node soap client
     */
    wsdlUrl: string;
    /**
     * Actual URL for SOAP RPC calls
     */
    url: string;

    logSecureKeys?: string[];

    secure: {
        cert: string;
        key: string;
        passphrase: string;
    };

    additionalHeaders?: [{ [key: string]: string }];
}
