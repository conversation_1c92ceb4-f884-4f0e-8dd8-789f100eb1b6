import { IsDefined, IsNotEmpty, IsOptional } from "class-validator";

export class GameLaunchUrlRequest {
    @IsDefined()
    @IsNotEmpty()
    userId: string;

    @IsDefined()
    @IsNotEmpty()
    token: string;

    @IsDefined()
    @IsNotEmpty()
    productName: string;

    @IsDefined()
    @IsNotEmpty()
    gameCode: string;

    @IsDefined()
    @IsNotEmpty()
    language: string;

    @IsDefined()
    @IsNotEmpty()
    currency: string;

    @IsDefined()
    @IsNotEmpty()
    realMode: string;

    @IsDefined()
    @IsNotEmpty()
    platform: string;

    @IsDefined()
    @IsNotEmpty()
    operator: string;

    // optional param for testing - we need to be able to pick right merchant type with this param
    @IsOptional()
    @IsNotEmpty()
    merchantType?: string;

    @IsOptional()
    @IsNotEmpty()
    jurisdiction?: BetfairJurisdiction;
}

export enum BetfairJurisdiction {
    INTERNATIONAL = "international",
    ITALY = "italy",
    SWEDEN = "sweden",
    SPAIN = "spain",
    ROMANIA = "romania"
}

export enum BetfairJPTransactionType {
    WIN = "WIN",
    CONTRIBUTION = "CONTRIBUTION"
}
